import { NextRequest, NextResponse } from "next/server";
import { unstable_noStore } from "next/cache";
import { PropertyModel } from "@/app/models/Property";
import { PropertyBusiness } from "./business";

export const dynamic = 'force-dynamic';

// ------- //
// - GET - //
// ------- //

// Get by "Id" or get all
export async function GET(request: NextRequest) {
    try {
        unstable_noStore();
        const params = new URL(request.url).searchParams;
        const id = params.get("id");
        const getAll = params.get("all") === "true";

        let result;
        if (getAll) {
            result = await PropertyBusiness.GetList();
        } else if (id) {
            result = await PropertyBusiness.Get(id);
        } else {
            return NextResponse.json({ error: "Missing 'id' or 'all' parameter" }, { status: 400 });
        }

        return NextResponse.json({ result }, { status: 200 });

    } catch (error) {
        console.log(error);
        return NextResponse.json({ error }, { status: 500 });
    }
}

// ---------- //
// - CREATE - //
// ---------- //

export async function PUT(request: NextRequest) {
    try {
        const params = new URL(request.url).searchParams;
        const isList = params.get("list") === "true";

        if (isList) {
            const dataList: PropertyModel[] = await request.json();
            await PropertyBusiness.CreateList(dataList);
        } else {
            const data: PropertyModel = await request.json();
            await PropertyBusiness.Create(data);
        }

        return NextResponse.json({ status: 200 });

    } catch (error) {
        console.log(error);
        return NextResponse.json({ error }, { status: 500 });
    }
}

// ---------- //
// - UPDATE - //
// ---------- //

export async function POST(request: NextRequest) {
    try {
        const params = new URL(request.url).searchParams;
        const isList = params.get("list") === "true";
        const sortOrderOnly = params.get("sortOrder") === "true";

        if (isList) {
            const dataList: PropertyModel[] = await request.json();
            await PropertyBusiness.UpdateList(dataList);
        } else if (sortOrderOnly) {
            const { id, sortOrder } = await request.json();
            await PropertyBusiness.UpdateSortOrder(id, sortOrder);
        } else {
            const data: PropertyModel = await request.json();
            await PropertyBusiness.Update(data);
        }

        return NextResponse.json({ status: 200 });

    } catch (error) {
        console.log(error);
        return NextResponse.json({ error }, { status: 500 });
    }
}

// ---------- //
// - DELETE - //
// ---------- //

export async function DELETE(request: NextRequest) {
    try {
        const params = new URL(request.url).searchParams;
        const id = params.get("id");
        const ids = params.get("ids");

        if (ids) {
            const idList: string[] = ids.split(',');
            await PropertyBusiness.DeleteList(idList);
        } else if (id) {
            await PropertyBusiness.Delete(id);
        } else {
            return NextResponse.json({ error: "Missing 'id' or 'ids' parameter" }, { status: 400 });
        }

        return NextResponse.json({ status: 200 });

    } catch (error) {
        console.log(error);
        return NextResponse.json({ error }, { status: 500 });
    }
}
