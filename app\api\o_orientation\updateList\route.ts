import { NextRequest, NextResponse } from "next/server";
import { O_OrientationModel } from "@/app/models/O_Orientation";
import { O_OrientationBusiness } from "../business";

export async function POST(request: NextRequest) {
    try {
        const dataList: O_OrientationModel[] = await request.json();
        await O_OrientationBusiness.UpdateList(dataList);
        return NextResponse.json({ status: 200 });
    } catch (error) {
        console.log(error);
        return NextResponse.json({ error }, { status: 500 });
    }
}
