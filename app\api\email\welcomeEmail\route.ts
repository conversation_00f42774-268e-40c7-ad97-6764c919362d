import { NextRequest, NextResponse } from 'next/server';
import { EmailBusiness } from '../business';
 
export async function POST(request: NextRequest) {

    try {
        const { name, email } = await request.json();
        await EmailBusiness.SendWelcomeEmail(name, email);
        return NextResponse.json({ status: 200 });
    } catch (error) {
        console.log(error);
        return NextResponse.json({ error }, { status: 500 });
    }

}