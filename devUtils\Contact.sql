SELECT "contact"."Id"
      ,"contact"."UserId"
      ,CONCAT("user"."FirstName", ' ', "user"."LastName") "__User"
      ,"user"."Email" "__UserEmail"
      ,"contact"."Name"
      ,"contact"."Email"
      ,"contact"."Phone"
      ,"contact"."Message"
      ,"contact"."CreatedAt"
FROM public."Contact" "contact"
LEFT JOIN public."User" "user" ON "contact"."UserId" = "user"."Id"
WHERE 1=1
ORDER BY "contact"."CreatedAt" DESC;
