"use client"

import { J<PERSON>_Utils } from '@/app/Utils';
import React, { ReactNode, useState, useCallback, useEffect } from 'react';
import Image from 'next/image';
import styles from './JC_List.module.scss';
import { JC_ListHeader } from './JC_ListHeader';
import { JC_ListPagingModel } from '@/app/models/ComponentModels/JC_ListPagingModel';

export default function JC_List<T>(_: Readonly<{
    overrideClass?: string;
    service: () => Promise<T[]>;
    paging?: JC_ListPagingModel;
    headers: JC_ListHeader[];
    row: (item: T) => ReactNode;
    defaultSortKey?: string;
    defaultSortDirection?: 'asc' | 'desc';
}>) {
    // - STATE - //
    const [items, setItems] = useState<T[]>([]);
    const [isLoading, setIsLoading] = useState(true);
    const [sortKey, setSortKey] = useState<string | undefined>(_.defaultSortKey);
    const [sortDir, setSortDir] = useState<'asc' | 'desc'>(_.defaultSortDirection || 'asc');

    // Track if user has explicitly selected a sort
    // Initially false because we start with the default sort
    const [userSelectedSort, setUserSelectedSort] = useState<boolean>(false);

    // - EFFECTS - //
    useEffect(() => {
        const loadData = async () => {
            try {
                setIsLoading(true);
                const data = await _.service();
                setItems(data);
            } catch (error) {
                console.error('Error loading data:', error);
                setItems([]);
            } finally {
                setIsLoading(false);
            }
        };

        loadData();
    }, [_.service, _.paging]);

    // - HANDLERS - //

    const handleSort = useCallback((key: string) => {
        // Check if this is the default sort key and direction
        const isDefaultSort = key === _.defaultSortKey &&
                             (!userSelectedSort && sortDir === _.defaultSortDirection);

        // If clicking on the default sort column for the first time, keep same direction but mark as user-selected
        if (isDefaultSort) {
            setUserSelectedSort(true);
            setSortKey(key);
            setSortDir(_.defaultSortDirection || 'asc');
            return;
        }

        // If no previous sort key or different key, sort ascending
        if (!sortKey || sortKey !== key) {
            setUserSelectedSort(true);
            setSortKey(key);
            setSortDir('asc');
            return;
        }

        // If already sorting by this key in ascending order, switch to descending
        if (sortDir === 'asc') {
            setUserSelectedSort(true);
            setSortKey(key);
            setSortDir('desc');
            return;
        }

        // If already sorting by this key in descending order, remove sort and fall back to default
        setUserSelectedSort(false);
        setSortKey(_.defaultSortKey);
        setSortDir(_.defaultSortDirection || 'asc');
    }, [_.defaultSortKey, _.defaultSortDirection, userSelectedSort, sortKey, sortDir]);

    const sortItems = (items: T[], key?: string, direction?: 'asc' | 'desc') => {
        if (!key || !direction) return items;

        return [...items].sort((a, b) => {
            // Access the property directly using the sort key
            const aValue = (a as any)[key];
            const bValue = (b as any)[key];

            // Handle different types of values
            if (aValue === bValue) return 0;

            // Handle null/undefined values
            if (aValue == null) return 1;
            if (bValue == null) return -1;

            // Handle different data types
            if (typeof aValue === 'string' && typeof bValue === 'string') {
                // Check if both strings are actually numbers
                const aIsNumber = !isNaN(Number(aValue)) && !isNaN(parseFloat(aValue));
                const bIsNumber = !isNaN(Number(bValue)) && !isNaN(parseFloat(bValue));

                if (aIsNumber && bIsNumber) {
                    // Both are numbers, compare numerically
                    const aNum = parseFloat(aValue);
                    const bNum = parseFloat(bValue);
                    return direction === 'asc'
                        ? (aNum > bNum ? 1 : aNum < bNum ? -1 : 0)
                        : (bNum > aNum ? 1 : bNum < aNum ? -1 : 0);
                } else {
                    // At least one is not a number, compare as strings
                    return direction === 'asc'
                        ? aValue.localeCompare(bValue)
                        : bValue.localeCompare(aValue);
                }
            }

            // Handle numbers and other comparable types
            return direction === 'asc'
                ? (aValue > bValue ? 1 : -1)
                : (bValue > aValue ? 1 : -1);
        });
    };

    // Get sorted items for rendering
    const displayItems = sortKey && items ? sortItems(items, sortKey, sortDir) : items;

    
    // - MAIN - //

    return (
        <table className={`${styles.table} ${!JC_Utils.stringNullOrEmpty(_.overrideClass) ? _.overrideClass : ''}`}>
            <thead>
                <tr>
                    {_.headers.map((header) => (
                        <th
                            key={header.sortKey}
                            className={`
                                ${styles.sortable}
                                ${header.hideOnTeenyTiny ? styles.hideOnTeenyTiny : ''}
                                ${header.hideOnTiny ? styles.hideOnTiny : ''}
                                ${header.hideOnSmall ? styles.hideOnSmall : ''}
                                ${header.hideOnMedium ? styles.hideOnMedium : ''}
                                ${header.hideOnLarge ? styles.hideOnLarge : ''}
                            `}
                            onClick={() => handleSort(header.sortKey)}
                        >
                            <div className={styles.headerLabelContainer}>
                                <div className={styles.headerLabel}>
                                    {header.label}
                                </div>
                                {sortKey === header.sortKey && userSelectedSort && (
                                    <Image
                                        src="/icons/Chevron.webp"
                                        width={0}
                                        height={0}
                                        alt="Sort indicator"
                                        className={styles.sortIndicator}
                                        style={{
                                            transform: sortDir === 'asc' ? 'rotate(180deg)' : 'rotate(0deg)'
                                        }}
                                        unoptimized
                                    />
                                )}
                            </div>
                        </th>
                    ))}
                </tr>
            </thead>
            <tbody>
                {isLoading ? (
                    <tr className={styles.noDataRow}>
                        <td colSpan={100} className={styles.noDataCell}>Loading...</td>
                    </tr>
                ) : (!displayItems || displayItems.length === 0) ? (
                    <tr className={styles.noDataRow}>
                        <td colSpan={100} className={styles.noDataCell}>No data yet.</td>
                    </tr>
                ) : (
                    displayItems.map((item) => {
                        // Get the original row element
                        const originalRow = _.row(item);

                        // Clone the element and add the tableRow class
                        if (React.isValidElement(originalRow)) {
                            // Clone the row element with the tableRow class
                            const rowElement = React.cloneElement(originalRow, {
                                className: `${styles.tableRow} ${originalRow.props.className || ''}`
                            } as React.HTMLAttributes<HTMLElement>);

                            // Apply responsive classes to the cells
                            if (React.isValidElement(rowElement)) {
                                // Type assertion to access props safely
                                const rowProps = rowElement.props as { children?: React.ReactNode };

                                if (rowProps.children) {
                                    const cells = React.Children.toArray(rowProps.children);
                                    const cellsWithClasses = cells.map((cell, index) => {
                                        if (React.isValidElement(cell) && index < _.headers.length) {
                                            const header = _.headers[index];
                                            // Type assertion for cell props
                                            const cellProps = cell.props as { className?: string };

                                            return React.cloneElement(cell, {
                                                className: `
                                                    ${cellProps.className || ''}
                                                    ${header.hideOnTeenyTiny ? styles.hideOnTeenyTiny : ''}
                                                    ${header.hideOnTiny ? styles.hideOnTiny : ''}
                                                    ${header.hideOnSmall ? styles.hideOnSmall : ''}
                                                    ${header.hideOnMedium ? styles.hideOnMedium : ''}
                                                    ${header.hideOnLarge ? styles.hideOnLarge : ''}
                                                `
                                            } as React.HTMLAttributes<HTMLElement>);
                                        }
                                        return cell;
                                    });

                                    return React.cloneElement(rowElement, {}, ...cellsWithClasses);
                                }
                            }

                            return rowElement;
                        }

                        return originalRow;
                    })
                )}
            </tbody>
        </table>
    );
}
