@echo off
setlocal enabledelayedexpansion

echo WebP Converter Script
echo ---------------------
echo This script will convert PNG, JPG, JPEG, GIF, and SVG files to WebP format.
echo WebP files will be saved in the current directory.
echo If a filename conflict occurs, the extension will be appended to the filename.
echo All original files will be moved to a "#Originals" folder.
echo Note: Existing WebP files will NOT be moved to the "#Originals" folder.
echo Note: SVG files will be converted to WebP with dimensions of 150x150 pixels, preserving aspect ratio and transparency.
echo.

REM Check if FFmpeg is installed
where ffmpeg >nul 2>&1
if %ERRORLEVEL% NEQ 0 (
    echo ERROR: FFmpeg is not installed or not in your PATH.
    echo Please install FFmpeg from https://ffmpeg.org/download.html
    echo and make sure it's added to your system PATH.
    echo.
    pause
    exit /b 1
)

REM Using Inkscape from PATH for SVG conversion
set "INKSCAPE_PATH=inkscape"

REM Set quality parameters
set STATIC_QUALITY=90
set GIF_QUALITY=90

REM Get current directory
set "SOURCE_DIR=%~dp0"
echo Processing files in: %SOURCE_DIR%
echo.

REM Create originals directory
set "BACKUP_DIR=%SOURCE_DIR%#Originals\"
if not exist "%BACKUP_DIR%" (
    echo Creating originals directory: %BACKUP_DIR%
    mkdir "%BACKUP_DIR%"
    echo Created originals directory: %BACKUP_DIR%
) else (
    echo Originals directory already exists: %BACKUP_DIR%
)
echo.

set /a TOTAL_FILES=0
set /a CONVERTED_FILES=0
set /a SKIPPED_FILES=0

for %%F in ("%SOURCE_DIR%*.png" "%SOURCE_DIR%*.jpg" "%SOURCE_DIR%*.jpeg" "%SOURCE_DIR%*.gif" "%SOURCE_DIR%*.svg") do (
    set /a TOTAL_FILES+=1
)

if %TOTAL_FILES% EQU 0 (
    echo No image files found to convert.
    pause
    exit /b 0
)

echo Found %TOTAL_FILES% files to process.
echo.

REM Process PNG files
echo Processing PNG files...
for %%F in ("%SOURCE_DIR%*.png") do (
    set "FILENAME=%%~nF"
    set "EXTENSION=%%~xF"
    set "EXTENSION_CLEAN=%%~xF"
    set "EXTENSION_CLEAN=!EXTENSION_CLEAN:.=!"
    set "WEBP_FILE=!FILENAME!.webp"
    set "WEBP_FILE_ALT=!FILENAME! (!EXTENSION_CLEAN!).webp"

    echo Converting: %%~nxF to !WEBP_FILE!

    REM Check if WebP version already exists
    if exist "%SOURCE_DIR%!WEBP_FILE!" (
        REM Check if we need to use the alternative filename with extension
        echo - WebP file already exists, using alternative filename
        set "WEBP_FILE=!WEBP_FILE_ALT!"

        REM Check if even the alternative filename exists
        if exist "%SOURCE_DIR%!WEBP_FILE!" (
            echo - Skipped: Both standard and alternative WebP versions already exist
            set /a SKIPPED_FILES+=1
        ) else (
            REM Convert using the alternative filename
            ffmpeg -i "%%F" -y -lossless 0 -q:v %STATIC_QUALITY% "%SOURCE_DIR%!WEBP_FILE!" 2>nul

            if !ERRORLEVEL! EQU 0 (
                echo - Success: Converted to WebP with alternative filename
                set /a CONVERTED_FILES+=1

                REM Move original file to backup folder
                move "%%F" "%BACKUP_DIR%" >nul
                echo - Moved original to backup folder
            ) else (
                echo - Error: Failed to convert
            )
        )
    ) else (
        REM Convert static image to WebP (redirect output to nul to suppress messages)
        ffmpeg -i "%%F" -y -lossless 0 -q:v %STATIC_QUALITY% "%SOURCE_DIR%!WEBP_FILE!" 2>nul

        if !ERRORLEVEL! EQU 0 (
            echo - Success: Converted to WebP
            set /a CONVERTED_FILES+=1

            REM Move original file to backup folder
            move "%%F" "%BACKUP_DIR%" >nul
            echo - Moved original to backup folder
        ) else (
            echo - Error: Failed to convert
        )
    )
    echo.
)

REM Process JPG files
echo Processing JPG files...
for %%F in ("%SOURCE_DIR%*.jpg") do (
    set "FILENAME=%%~nF"
    set "EXTENSION=%%~xF"
    set "EXTENSION_CLEAN=%%~xF"
    set "EXTENSION_CLEAN=!EXTENSION_CLEAN:.=!"
    set "WEBP_FILE=!FILENAME!.webp"
    set "WEBP_FILE_ALT=!FILENAME! (!EXTENSION_CLEAN!).webp"

    echo Converting: %%~nxF to !WEBP_FILE!

    REM Check if WebP version already exists
    if exist "%SOURCE_DIR%!WEBP_FILE!" (
        REM Check if we need to use the alternative filename with extension
        echo - WebP file already exists, using alternative filename
        set "WEBP_FILE=!WEBP_FILE_ALT!"

        REM Check if even the alternative filename exists
        if exist "%SOURCE_DIR%!WEBP_FILE!" (
            echo - Skipped: Both standard and alternative WebP versions already exist
            set /a SKIPPED_FILES+=1
        ) else (
            REM Convert using the alternative filename
            ffmpeg -i "%%F" -y -lossless 0 -q:v %STATIC_QUALITY% "%SOURCE_DIR%!WEBP_FILE!" 2>nul

            if !ERRORLEVEL! EQU 0 (
                echo - Success: Converted to WebP with alternative filename
                set /a CONVERTED_FILES+=1

                REM Move original file to backup folder
                move "%%F" "%BACKUP_DIR%" >nul
                echo - Moved original to backup folder
            ) else (
                echo - Error: Failed to convert
            )
        )
    ) else (
        REM Convert static image to WebP (redirect output to nul to suppress messages)
        ffmpeg -i "%%F" -y -lossless 0 -q:v %STATIC_QUALITY% "%SOURCE_DIR%!WEBP_FILE!" 2>nul

        if !ERRORLEVEL! EQU 0 (
            echo - Success: Converted to WebP
            set /a CONVERTED_FILES+=1

            REM Move original file to backup folder
            move "%%F" "%BACKUP_DIR%" >nul
            echo - Moved original to backup folder
        ) else (
            echo - Error: Failed to convert
        )
    )
    echo.
)

REM Process JPEG files
echo Processing JPEG files...
for %%F in ("%SOURCE_DIR%*.jpeg") do (
    set "FILENAME=%%~nF"
    set "EXTENSION=%%~xF"
    set "EXTENSION_CLEAN=%%~xF"
    set "EXTENSION_CLEAN=!EXTENSION_CLEAN:.=!"
    set "WEBP_FILE=!FILENAME!.webp"
    set "WEBP_FILE_ALT=!FILENAME! (!EXTENSION_CLEAN!).webp"

    echo Converting: %%~nxF to !WEBP_FILE!

    REM Check if WebP version already exists
    if exist "%SOURCE_DIR%!WEBP_FILE!" (
        REM Check if we need to use the alternative filename with extension
        echo - WebP file already exists, using alternative filename
        set "WEBP_FILE=!WEBP_FILE_ALT!"

        REM Check if even the alternative filename exists
        if exist "%SOURCE_DIR%!WEBP_FILE!" (
            echo - Skipped: Both standard and alternative WebP versions already exist
            set /a SKIPPED_FILES+=1
        ) else (
            REM Convert using the alternative filename
            ffmpeg -i "%%F" -y -lossless 0 -q:v %STATIC_QUALITY% "%SOURCE_DIR%!WEBP_FILE!" 2>nul

            if !ERRORLEVEL! EQU 0 (
                echo - Success: Converted to WebP with alternative filename
                set /a CONVERTED_FILES+=1

                REM Move original file to backup folder
                move "%%F" "%BACKUP_DIR%" >nul
                echo - Moved original to backup folder
            ) else (
                echo - Error: Failed to convert
            )
        )
    ) else (
        REM Convert static image to WebP (redirect output to nul to suppress messages)
        ffmpeg -i "%%F" -y -lossless 0 -q:v %STATIC_QUALITY% "%SOURCE_DIR%!WEBP_FILE!" 2>nul

        if !ERRORLEVEL! EQU 0 (
            echo - Success: Converted to WebP
            set /a CONVERTED_FILES+=1

            REM Move original file to backup folder
            move "%%F" "%BACKUP_DIR%" >nul
            echo - Moved original to backup folder
        ) else (
            echo - Error: Failed to convert
        )
    )
    echo.
)

REM Process GIF files separately to preserve animation
echo Processing GIF files...
for %%F in ("%SOURCE_DIR%*.gif") do (
    set "FILENAME=%%~nF"
    set "EXTENSION=gif"
    set "WEBP_FILE=!FILENAME!.webp"
    set "WEBP_FILE_ALT=!FILENAME! (gif).webp"

    echo Converting animated GIF: %%~nxF to !WEBP_FILE!

    REM Check if WebP version already exists
    if exist "%SOURCE_DIR%!WEBP_FILE!" (
        REM Check if we need to use the alternative filename with extension
        echo - WebP file already exists, using alternative filename
        set "WEBP_FILE=!WEBP_FILE_ALT!"

        REM Check if even the alternative filename exists
        if exist "%SOURCE_DIR%!WEBP_FILE!" (
            echo - Skipped: Both standard and alternative WebP versions already exist
            set /a SKIPPED_FILES+=1
        ) else (
            REM Convert using the alternative filename
            ffmpeg -i "%%F" -y -vf "scale=trunc(iw/2)*2:trunc(ih/2)*2" -c:v libwebp -lossless 0 -q:v %GIF_QUALITY% -loop 0 -preset picture -an -vsync 0 "%SOURCE_DIR%!WEBP_FILE!" 2>nul

            if !ERRORLEVEL! EQU 0 (
                echo - Success: Converted to animated WebP with alternative filename
                set /a CONVERTED_FILES+=1

                REM Move original file to backup folder
                move "%%F" "%BACKUP_DIR%" >nul
                echo - Moved original to backup folder
            ) else (
                echo - Error: Failed to convert
            )
        )
    ) else (
        REM Convert animated GIF to animated WebP (redirect output to nul to suppress messages)
        ffmpeg -i "%%F" -y -vf "scale=trunc(iw/2)*2:trunc(ih/2)*2" -c:v libwebp -lossless 0 -q:v %GIF_QUALITY% -loop 0 -preset picture -an -vsync 0 "%SOURCE_DIR%!WEBP_FILE!" 2>nul

        if !ERRORLEVEL! EQU 0 (
            echo - Success: Converted to animated WebP
            set /a CONVERTED_FILES+=1

            REM Move original file to backup folder
            move "%%F" "%BACKUP_DIR%" >nul
            echo - Moved original to backup folder
        ) else (
            echo - Error: Failed to convert
        )
    )
    echo.
)

REM Process SVG files
echo Processing SVG files...
for %%F in ("%SOURCE_DIR%*.svg") do (
    set "FILENAME=%%~nF"
    set "EXTENSION=%%~xF"
    set "EXTENSION_CLEAN=%%~xF"
    set "EXTENSION_CLEAN=!EXTENSION_CLEAN:.=!"
    set "WEBP_FILE=!FILENAME!.webp"
    set "WEBP_FILE_ALT=!FILENAME! (!EXTENSION_CLEAN!).webp"
    set "TEMP_PNG=!FILENAME!_temp.png"

    echo Converting: %%~nxF to !WEBP_FILE!

    REM Check if WebP version already exists
    if exist "%SOURCE_DIR%!WEBP_FILE!" (
        REM Check if we need to use the alternative filename with extension
        echo - WebP file already exists, using alternative filename
        set "WEBP_FILE=!WEBP_FILE_ALT!"

        REM Check if even the alternative filename exists
        if exist "%SOURCE_DIR%!WEBP_FILE!" (
            echo - Skipped: Both standard and alternative WebP versions already exist
            set /a SKIPPED_FILES+=1
        ) else (
            REM Step 1: Convert SVG to PNG using Inkscape preserving aspect ratio and transparency
            REM Try newer Inkscape command-line syntax first
            "%INKSCAPE_PATH%" --export-filename="%SOURCE_DIR%!TEMP_PNG!" --export-area-drawing --export-background-opacity=0 --export-width=150 "%%F" >nul 2>nul

            REM If that fails, try older Inkscape command-line syntax
            if !ERRORLEVEL! NEQ 0 (
                "%INKSCAPE_PATH%" -z -e "%SOURCE_DIR%!TEMP_PNG!" -D -y 0 -w 150 "%%F" >nul 2>nul
            )

            if !ERRORLEVEL! EQU 0 (
                REM Step 2: Convert PNG to WebP using FFmpeg with padding to ensure 150x150 and preserving transparency
                ffmpeg -i "%SOURCE_DIR%!TEMP_PNG!" -y -vf "scale=w=150:h=150:force_original_aspect_ratio=decrease,pad=150:150:(ow-iw)/2:(oh-ih)/2:color=0x00000000" -lossless 0 -q:v %STATIC_QUALITY% -pix_fmt yuva420p "%SOURCE_DIR%!WEBP_FILE!" 2>nul

                if !ERRORLEVEL! EQU 0 (
                    echo - Success: Converted to WebP with alternative filename
                    set /a CONVERTED_FILES+=1

                    REM Delete temporary PNG file
                    del "%SOURCE_DIR%!TEMP_PNG!" >nul 2>nul

                    REM Move original file to backup folder
                    move "%%F" "%BACKUP_DIR%" >nul 2>nul
                    echo - Moved original to backup folder
                ) else (
                    echo - Error: Failed to convert PNG to WebP

                    REM Delete temporary PNG file
                    del "%SOURCE_DIR%!TEMP_PNG!" >nul 2>nul
                )
            ) else (
                echo - Error: Failed to convert SVG to PNG
            )
        )
    ) else (
        REM Step 1: Convert SVG to PNG using Inkscape preserving aspect ratio and transparency
        REM Try newer Inkscape command-line syntax first
        "%INKSCAPE_PATH%" --export-filename="%SOURCE_DIR%!TEMP_PNG!" --export-area-drawing --export-background-opacity=0 --export-width=150 "%%F" >nul 2>nul

        REM If that fails, try older Inkscape command-line syntax
        if !ERRORLEVEL! NEQ 0 (
            "%INKSCAPE_PATH%" -z -e "%SOURCE_DIR%!TEMP_PNG!" -D -y 0 -w 150 "%%F" >nul 2>nul
        )

        if !ERRORLEVEL! EQU 0 (
            REM Step 2: Convert PNG to WebP using FFmpeg with padding to ensure 150x150 and preserving transparency
            ffmpeg -i "%SOURCE_DIR%!TEMP_PNG!" -y -vf "scale=w=150:h=150:force_original_aspect_ratio=decrease,pad=150:150:(ow-iw)/2:(oh-ih)/2:color=0x00000000" -lossless 0 -q:v %STATIC_QUALITY% -pix_fmt yuva420p "%SOURCE_DIR%!WEBP_FILE!" 2>nul

            if !ERRORLEVEL! EQU 0 (
                echo - Success: Converted to WebP
                set /a CONVERTED_FILES+=1

                REM Delete temporary PNG file
                del "%SOURCE_DIR%!TEMP_PNG!" >nul 2>nul

                REM Move original file to backup folder
                move "%%F" "%BACKUP_DIR%" >nul 2>nul
                echo - Moved original to backup folder
            ) else (
                echo - Error: Failed to convert PNG to WebP

                REM Delete temporary PNG file
                del "%SOURCE_DIR%!TEMP_PNG!" >nul 2>nul
            )
        ) else (
            echo - Error: Failed to convert SVG to PNG
        )
    )
    echo.
)

REM Move any other files (except the batch script itself, webp files, and log files) to originals folder
echo Moving other files to originals folder...
for %%F in ("%SOURCE_DIR%*.*") do (
    if not "%%~nxF"=="#ConvertToWebp.bat" (
        if not "%%~xF"==".webp" (
            if not "%%~xF"==".bat" (
                if not "%%~xF"==".txt" (
                    echo Moving: %%~nxF to originals folder
                    move "%%F" "%BACKUP_DIR%" >nul 2>nul
                )
            )
        )
    )
)

echo Conversion complete!
echo Successfully converted: %CONVERTED_FILES% files
echo Skipped: %SKIPPED_FILES% files
echo WebP files are in the current directory.
echo All original files were moved to: %BACKUP_DIR%
echo Note: Existing WebP files were NOT moved to the "#Originals" folder.
echo.

pause