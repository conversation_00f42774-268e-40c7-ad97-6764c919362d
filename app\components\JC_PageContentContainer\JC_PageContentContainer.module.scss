@import '../../global';

.contentContainer {
    position: relative;
    width: auto;
    min-width: 300px;
    min-height: 200px;
    background-color: transparent;
    padding: 30px;
    display: flex;
    flex-direction: column;
    align-items: center;
    margin-bottom: 100px;

    &.withBorder {
        @include containerShadow;
        border: 2px solid $primaryColor;
        border-radius: $largeBorderRadius;
        background-color: $offBlack;
    }
}

// - SCREEN SIZES - //

// Medium and small screen sizes have no specific changes

@media (max-width: $tinyScreenSize) {
    .contentContainer {
        padding: 30px 22px;
    }
}
