
## Adding Font


```javascript
import { Open_Sans } from 'next/font/google';

const openSans = Open_Sans({ subsets: ["latin"], variable: '--font-open-sans' });

<div className={`${openSans.variable}`}>
    ...
</div>
```
Can now use in this component or any child component:
```css
font-family: var(--font-open-sans);
```

<br/>


## URL Parameters

```javascript
import { useSearchParams } from 'next/navigation'

let idParam = useSearchParams().get("id") as string;
useEffect(() => {
    if (!StringNullOrEmpty(idParam)) {
        GetList(idParam).then(list => ...);
    }
}, []);
```

Must wrap the component in a Suspense:

```javascript
export default function Page_Product_Suspense() {
    return <Suspense><Page_Product /></Suspense>
}

function Page_Product() { ... }
```

<br/>


## Confirmation Modal

```javascript
const [confirmationModalData, setConfirmationModalData] = useState<JC_ConfirmationModalUsageModel | null>();
const [confirmationLoading, setConfirmationLoading] = useState<boolean>(false);
```

```javascript
async function resetPassword() {
    setConfirmationModalData({
        width: "380px",
        title: "Reset Password Email",
        text: `We will send you a "Reset Password" link to your email. Continue?`,
        submitButtons: [{
            text: "Send Email",
            onSubmit: async () => {
                setConfirmationLoading(true);
                await JC_Update("user/triggerResetPasswordToken", { email: session.data!.user.Email });
                setConfirmationLoading(false);
                setConfirmationModalData(null);
                ShowToastSuccess("A password reset link has been sent to your email!");
            }
        }]
    });
}
```

```javascript
{confirmationModalData &&
<JC_ModalConfirmation
    width={confirmationModalData.width}
    title={confirmationModalData.title}
    text={confirmationModalData.text}
    isOpen={confirmationModalData != null}
    onCancel={() => setConfirmationModalData(null)}
    submitButtons={confirmationModalData.submitButtons}
    isLoading={confirmationLoading}
/>}
```

<br/>


## To Make Sure Backend Doesn't Cache

```javascript
import { unstable_noStore } from "next/cache";

export async function GET(request: NextRequest) {
    
    try {
        unstable_noStore();
        ...
        return NextResponse.json({ result }, { status: 200 });
    } catch (error) {
        console.log(error);
        return NextResponse.json({ error }, { status: 500 });
    }

}
```

<br/>


## Send Email


```javascript
import { Resend } from 'resend';

const emailData:Contact = {
    Name: "Jimmy Recard",
    Email: "jim@<EMAIL>",
    Phone: 0430123456,
    Message: "Hey mate do you think Chic Chip crumpets will be in stock soon?"
};
const resend = new Resend(process.env.RESEND_API_KEY);
await resend.emails.send({
    from: 'Casella Web <<EMAIL>>',
    to: '<EMAIL>',
    cc: '<EMAIL>',
    subject: 'Test Subject Mate',
    react: Template_ContactEmail(emailData)
});
```

<br/>


## Refresh User Session Data

When need to update session data:
* Set GlobalSettings value (eg. "ForceRefreshAuthToken") to "1".
* Client side, update "session" data
* Server side, update token in "jwt()" callback

Client side:

```javascript
import { useSession } from "next-auth/react";

const session = useSession();

// Eg. User just updated their details
let newUser = {
    ...session.data!.user,
    FirstName: firstName,
    LastName: lastName,
    Phone: !StringNullOrEmpty(phone) ? phone : undefined,
    CompanyName: !StringNullOrEmpty(company) ? company : undefined
};
// Update db User
await JC_Update<UserModel>("user", newUser);
// Trigger "jwt()" callback to refresh User from db
await JC_Update<GlobalSettingsModel>("globalSettings", {
    Code: "ForceRefreshAuthToken",
    Description: "",
    Value: "1"
});
// Update the session with new data (need this plus the update in "jwt()" callback to get update showing properly)
const newSession = session.data;
newSession!.user = newUser;
await session.update(newSession);
```

Server side:

```javascript
async jwt({ token, user }) {
    if (user || (await GetGlobalSettingsValue("ForceRefreshAuthToken")).rows[0].Value == "1") {
        let dbUser:UserModel = (await (await GetUserById(token.sub as string)));
        token.dbUser = dbUser;
        token.PasswordHash = "";
        await UpdateGlobalSettingsValue("ForceRefreshAuthToken", "0");
    }
    return token;
}
```