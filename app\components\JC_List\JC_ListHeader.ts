export interface JC_ListHeader {
    label: string;
    sortKey: string;
    hideOnTeenyTiny?: boolean; // Hide on teeny tiny screens (< 600px)
    hideOnTiny?: boolean;      // Hide on tiny screens (< 790px)
    hideOnSmall?: boolean;     // Hide on small screens (< 1020px)
    hideOnMedium?: boolean;    // Hide on medium screens (< 1360px)
    hideOnLarge?: boolean;     // Hide on large screens (> 1500px)
}
