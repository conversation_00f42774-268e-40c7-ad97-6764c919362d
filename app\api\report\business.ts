import { sql } from "@vercel/postgres";
import { ReportModel } from "@/app/models/Report";
import { JC_Utils_Dates } from "@/app/Utils";

export class ReportBusiness {

    // - --- - //
    // - GET - //
    // - --- - //

    static async Get(id: string) {
        return (await sql<ReportModel>`
            SELECT "Id",
                   "PropertyId",
                   "UserId",
                   "Name",
                   "PostalAddress",
                   "ClientName",
                   "ClientPhone",
                   "ClientEmail",
                   "ClientPrincipalName",
                   "InspectionDate",
                   "InspectorNameOverride",
                   "InspectorPhoneOverride",
                   "InspectorQualificationOverride",
                   "FileId",
                   "CreatedAt",
                   "ModifiedAt",
                   "Deleted"
            FROM public."Report"
            WHERE "Id" = ${id}
              AND "Deleted" = 'False'
        `).rows[0];
    }

    static async GetList() {
        return (await sql<ReportModel>`
            SELECT "Id",
                   "PropertyId",
                   "UserId",
                   "Name",
                   "PostalAddress",
                   "ClientName",
                   "ClientPhone",
                   "ClientEmail",
                   "ClientPrincipalName",
                   "InspectionDate",
                   "InspectorNameOverride",
                   "InspectorPhoneOverride",
                   "InspectorQualificationOverride",
                   "FileId",
                   "CreatedAt",
                   "ModifiedAt",
                   "Deleted"
            FROM public."Report"
            WHERE "Deleted" = 'False'
            ORDER BY "CreatedAt" DESC
        `).rows;
    }

    static async GetByUserId(userId: string) {
        return (await sql<ReportModel>`
            SELECT "Id",
                   "PropertyId",
                   "UserId",
                   "Name",
                   "PostalAddress",
                   "ClientName",
                   "ClientPhone",
                   "ClientEmail",
                   "ClientPrincipalName",
                   "InspectionDate",
                   "InspectorNameOverride",
                   "InspectorPhoneOverride",
                   "InspectorQualificationOverride",
                   "FileId",
                   "CreatedAt",
                   "ModifiedAt",
                   "Deleted"
            FROM public."Report"
            WHERE "UserId" = ${userId}
              AND "Deleted" = 'False'
            ORDER BY "CreatedAt" DESC
        `).rows;
    }

    static async GetByPropertyId(propertyId: string) {
        return (await sql<ReportModel>`
            SELECT "Id",
                   "PropertyId",
                   "UserId",
                   "Name",
                   "PostalAddress",
                   "ClientName",
                   "ClientPhone",
                   "ClientEmail",
                   "ClientPrincipalName",
                   "InspectionDate",
                   "InspectorNameOverride",
                   "InspectorPhoneOverride",
                   "InspectorQualificationOverride",
                   "FileId",
                   "CreatedAt",
                   "ModifiedAt",
                   "Deleted"
            FROM public."Report"
            WHERE "PropertyId" = ${propertyId}
              AND "Deleted" = 'False'
            ORDER BY "CreatedAt" DESC
        `).rows;
    }

    // - ------ - //
    // - CREATE - //
    // - ------ - //

    static async Create(data: ReportModel) {
        await sql`
            INSERT INTO public."Report"
            (
                "Id",
                "PropertyId",
                "UserId",
                "Name",
                "PostalAddress",
                "ClientName",
                "ClientPhone",
                "ClientEmail",
                "ClientPrincipalName",
                "InspectionDate",
                "InspectorNameOverride",
                "InspectorPhoneOverride",
                "InspectorQualificationOverride",
                "FileId",
                "CreatedAt"
            )
            VALUES
            (
                ${data.Id},
                ${data.PropertyId},
                ${data.UserId},
                ${data.Name},
                ${data.PostalAddress},
                ${data.ClientName},
                ${data.ClientPhone},
                ${data.ClientEmail},
                ${data.ClientPrincipalName},
                ${JC_Utils_Dates.formatDateForPostgres(data.InspectionDate)},
                ${data.InspectorNameOverride},
                ${data.InspectorPhoneOverride},
                ${data.InspectorQualificationOverride},
                ${data.FileId},
                ${new Date().toUTCString()}
            )
        `;
    }

    static async CreateList(dataList: ReportModel[]) {
        for (const data of dataList) {
            await this.Create(data);
        }
    }

    // - ------ - //
    // - UPDATE - //
    // - ------ - //

    static async Update(data: ReportModel) {
        await sql`
            UPDATE public."Report"
            SET "PropertyId" = ${data.PropertyId},
                "UserId" = ${data.UserId},
                "Name" = ${data.Name},
                "PostalAddress" = ${data.PostalAddress},
                "ClientName" = ${data.ClientName},
                "ClientPhone" = ${data.ClientPhone},
                "ClientEmail" = ${data.ClientEmail},
                "ClientPrincipalName" = ${data.ClientPrincipalName},
                "InspectionDate" = ${JC_Utils_Dates.formatDateForPostgres(data.InspectionDate)},
                "InspectorNameOverride" = ${data.InspectorNameOverride},
                "InspectorPhoneOverride" = ${data.InspectorPhoneOverride},
                "InspectorQualificationOverride" = ${data.InspectorQualificationOverride},
                "FileId" = ${data.FileId},
                "ModifiedAt" = ${new Date().toUTCString()},
                "Deleted" = ${data.Deleted}
            WHERE "Id" = ${data.Id}
        `;
    }

    static async UpdateList(dataList: ReportModel[]) {
        for (const data of dataList) {
            await this.Update(data);
        }
    }

    // - ------ - //
    // - DELETE - //
    // - ------ - //

    static async Delete(id: string) {
        await sql`
            UPDATE public."Report"
            SET "Deleted" = 'True',
                "ModifiedAt" = ${new Date().toUTCString()}
            WHERE "Id" = ${id}
        `;
    }

    static async DeleteList(ids: string[]) {
        for (const id of ids) {
            await this.Delete(id);
        }
    }
}
