import { NextRequest, NextResponse } from "next/server";
import bcrypt from "bcryptjs";
import { UserBusiness } from "../business";
import { EmailBusiness } from "../../email/business";

export async function POST(request: NextRequest) {
    try {
        const { email } = await request.json();
        // Set token
        let newResetPasswordToken = await bcrypt.hash(email, 12);
        await UserBusiness.SetResetPasswordToken(email, newResetPasswordToken);
        // Send email
        await EmailBusiness.SendResetPasswordEmail(email, newResetPasswordToken);
        return NextResponse.json({ status: 200 });
    } catch (error) {
        console.log(error);
        return NextResponse.json({ error }, { status: 500 });
    }
}