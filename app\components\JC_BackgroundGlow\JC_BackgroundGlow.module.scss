@import '../../global';

.glowContainer {
    position: fixed;
    top: 0;
    left: 0;
    width: 100vw;
    height: 100vh;
    pointer-events: none;
    z-index: 0; /* Ensure it's not cut off */
    overflow: visible;
}

.glowElement {
    position: absolute;
    border-radius: 50%;
    background: radial-gradient(circle, rgba($primaryColor, 0.2) 0%, rgba($primaryColor, 0.1) 40%, rgba($primaryColor, 0) 70%);
    will-change: transform;

    &.mobile {
        animation: randomMovement 20s infinite alternate ease-in-out;
    }
}

@keyframes randomMovement {
    0% {
        transform: translate(10%, 10%);
    }
    25% {
        transform: translate(70%, 30%);
    }
    50% {
        transform: translate(30%, 70%);
    }
    75% {
        transform: translate(70%, 70%);
    }
    100% {
        transform: translate(10%, 10%);
    }
}
