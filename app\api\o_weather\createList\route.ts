import { NextRequest, NextResponse } from "next/server";
import { O_WeatherModel } from "@/app/models/O_Weather";
import { O_WeatherBusiness } from "../business";

export async function PUT(request: NextRequest) {
    try {
        const dataList: O_WeatherModel[] = await request.json();
        await O_WeatherBusiness.CreateList(dataList);
        return NextResponse.json({ status: 200 });
    } catch (error) {
        console.log(error);
        return NextResponse.json({ error }, { status: 500 });
    }
}
