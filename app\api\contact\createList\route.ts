import { NextRequest, NextResponse } from "next/server";
import { ContactModel } from "@/app/models/Contact";
import { ContactBusiness } from "../business";

export async function PUT(request: NextRequest) {
    try {
        const dataList: ContactModel[] = await request.json();
        await ContactBusiness.CreateList(dataList);
        return NextResponse.json({ status: 200 });
    } catch (error) {
        console.log(error);
        return NextResponse.json({ error }, { status: 500 });
    }
}
