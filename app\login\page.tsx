"use client"

import styles from "./page.module.scss";
import { useEffect, useState } from "react";
import { useRouter } from "next/navigation";
import JC_Form from "../components/JC_Form/JC_Form";
import JC_Title from "../components/JC_Title/JC_Title";
import { signIn } from "next-auth/react";
import { authenticate } from "../actions";
import { D_FieldModel_Email } from "../models/ComponentModels/JC_Field";
import { FieldTypeEnum } from "../enums/FieldType";
import { LocalStorageKeyEnum } from "../enums/LocalStorageKey";
import { JC_Utils } from "../Utils";

export default function Page_Login() {

    const router = useRouter();

    // - STATE - //

    const [loginEmail, setLoginEmail] = useState<string>("");
    const [loginPassword, setLoginPassword] = useState<string>("");
    const [isLoading, setIsLoading] = useState<boolean>(false);
    const [errorMessage, setErrorMessage] = useState<string>("");

    // - EFFECTS - //

    useEffect(() => {
        if (!JC_Utils.isOnMobile()) {
            setTimeout(() => (document.getElementById("login-first-input") as HTMLInputElement)?.select(), 0);
        }
        if (localStorage.getItem(LocalStorageKeyEnum.JC_ShowForgotPasswordSent) == "1") {
            JC_Utils.showToastSuccess("A password reset link has been sent to your email!");
            localStorage.setItem(LocalStorageKeyEnum.JC_ShowForgotPasswordSent, "0");
        }
    }, []);

    // - HANDLES - //

    async function login() {
        setIsLoading(true);
        setErrorMessage("");
        // Login, then go back "Home"
        localStorage.setItem(LocalStorageKeyEnum.JC_ShowLoggedInWelcome, "1");
        let result = await authenticate(loginEmail, loginPassword);
        // IF error
        if (result.error) {
            setErrorMessage(result.error);
            setIsLoading(false);
        // ELSE sign in again so session updates properly then it takes User to Home
        } else {
            await signIn("credentials", { email: loginEmail, password: loginPassword, callbackUrl: "/" });
        }
    }

    // - Main - //

    return (
        <div className={styles.mainContainer}>
            <div className={styles.formContainer}>

                {/* Title */}
                <JC_Title title="Login" />

                {/* Form */}
                <JC_Form
                    submitButtonText="Login"
                    onSubmit={login}
                    isLoading={isLoading}
                    errorMessage={errorMessage}
                    fields={[
                        // Email
                        {
                            ...D_FieldModel_Email(),
                            inputId:"login-first-input",
                            onChange: (newValue) => setLoginEmail(newValue),
                            value: loginEmail
                        },
                        // Password
                        {
                            inputId: "login-password-input",
                            type: FieldTypeEnum.Password,
                            label: "Password",
                            onChange: (newValue) => setLoginPassword(newValue),
                            value: loginPassword,
                            validate: (v:any) => JC_Utils.stringNullOrEmpty(v) ? "Enter a password." : ""
                        }
                    ]}
                />

                {/* Forgot Password */}
                <div className={styles.smallTextButton} onClick={() => router.push("forgotPassword")}>
                    Forgot Password?
                </div>

            </div>
        </div>
    );
}
