import { NextRequest, NextResponse } from "next/server";
import { PropertyModel } from "@/app/models/Property";
import { PropertyBusiness } from "../business";

export async function POST(request: NextRequest) {
    try {
        const dataList: PropertyModel[] = await request.json();
        await PropertyBusiness.UpdateList(dataList);
        return NextResponse.json({ status: 200 });
    } catch (error) {
        console.log(error);
        return NextResponse.json({ error }, { status: 500 });
    }
}
