import { NextRequest, NextResponse } from "next/server";
import { O_OccupiedModel } from "@/app/models/O_Occupied";
import { O_OccupiedBusiness } from "../business";

export async function POST(request: NextRequest) {
    try {
        const dataList: O_OccupiedModel[] = await request.json();
        await O_OccupiedBusiness.UpdateList(dataList);
        return NextResponse.json({ status: 200 });
    } catch (error) {
        console.log(error);
        return NextResponse.json({ error }, { status: 500 });
    }
}
