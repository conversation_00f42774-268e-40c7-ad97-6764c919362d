import { NextRequest, NextResponse } from 'next/server';
import { ContactModel } from '@/app/models/Contact';
import { EmailBusiness } from '../business';
 
export async function POST(request: NextRequest) {

    try {
        const emailData:ContactModel = await request.json();
        await EmailBusiness.SendContactEmail(emailData);
        return NextResponse.json({ status: 200 });
    } catch (error) {
        console.log(error);
        return NextResponse.json({ error }, { status: 500 });
    }

}