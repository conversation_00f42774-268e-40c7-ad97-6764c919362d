import { NextRequest, NextResponse } from "next/server";
import { UserModel } from "@/app/models/User";
import { UserBusiness } from "../business";

export async function POST(request: NextRequest) {
    try {
        const dataList: UserModel[] = await request.json();
        await UserBusiness.UpdateList(dataList);
        return NextResponse.json({ status: 200 });
    } catch (error) {
        console.log(error);
        return NextResponse.json({ error }, { status: 500 });
    }
}
