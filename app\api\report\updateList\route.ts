import { NextRequest, NextResponse } from "next/server";
import { ReportModel } from "@/app/models/Report";
import { ReportBusiness } from "../business";

export async function POST(request: NextRequest) {
    try {
        const dataList: ReportModel[] = await request.json();
        await ReportBusiness.UpdateList(dataList);
        return NextResponse.json({ status: 200 });
    } catch (error) {
        console.log(error);
        return NextResponse.json({ error }, { status: 500 });
    }
}
