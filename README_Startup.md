
## Startup

* Create outlook email for project (https://signup.live.com/signup) (<EMAIL>)
* Create Edge profile using email
    * Then, add gamil+phone by clicking "Manage how I sign in" as backups so it doesn't prompt for this later
    * Add profile to PickEdgeProfile
* Create Figma project
* Create+clone repo
* Copy everything in JCTemplateWeb (except ".git", ".next" and "node_modules" folders) into repo
* Make sure Sourcetree author set to "<EMAIL>"
* Push
* Vercel
    * Setup project on selected account, connect the repo (need to add repo to permissions by searching for something to show no results then click "Configure GitHub App")
        * Will probably fail when it first tries to deploy, just go to "Deployments" and re-deploy.
    * Setup db
        * Name format "casella-web-postgres"
        * Click "Copy Snippet"
        * Paste over db variables in "#Startup - 2.txt"
        * Add connection to Azure Data Studio
    * Setup file storage
        * On "Storage" tab click "Connect Database", select the existing blob
    * Add to JC_DbBackup's ".env" on local and on <PERSON>'s server, then run to make sure dbBackups folder is created and backup is created successfully
* Add info to "Accounts" note
* IF need Resend
    * Setup new account using client's email
    * Add client's custom domain
    * Add dns records to domain provider account and verify
    * Add API Key to "#Startup -2.txt"
* IF need Stripe
    * Create Stripe account
    * Add keys+secret to "#Startup.txt"
* IF need Google Maps, add site's domain to Google Maps API
* Setup dbml specific for project
* Push
* Complete rest of "Startup - X.txt"
* Push
* Run each "#Startup - X.txt" in Augment (don't need to run "#Startup - 1.txt" if not removing anything)
    * May need to run "continue" if stops since it will run a lot of things
    * Push after each "#Startup - X.txt"
* Add any .env.local variables to Vercel env variables
* Run "dbScripts/Init.sql" on db
* Run, login, navigate every page to make sure doesn't fail, fix until works
* Push
* Add favicon and any required images in "public", re-run to check
* Delete this file and "#Startup - X.txt" files