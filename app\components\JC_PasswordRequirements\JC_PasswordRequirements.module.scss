@import '../../global';

.mainContainer {
    margin-top: -6px;
    margin-bottom: -6px;

    ul {
        list-style-type: none;
        padding-left: 0;
        margin: 0;

        li {
            display: flex;
            align-items: center;
            margin-bottom: 4px;
            position: relative;
            font-size: 14px;
            width: 100%;
            padding-left: 12px; // Space for bullet point
            color: $secondaryColor;

            // Bullet point
            &::before {
                content: '•';
                position: absolute;
                left: 0;
                font-size: 14px;
                color: $secondaryColor;
            }

            &.valid {
                color: $primaryColor;

                &::before {
                    color: $primaryColor;
                }
            }

            &.invalid {
                color: $errorColor;

                &::before {
                    color: $errorColor;
                }
            }

            span {
                flex-grow: 1;
            }

            // Reserve space for the checkmark to prevent layout shifts
            &::after {
                content: '';
                display: inline-block;
                width: 22px;
                flex-shrink: 0;
                visibility: hidden;
            }

            .checkmark {
                width: 14px;
                height: 14px;
                position: absolute;
                right: 12px;

                &:before {
                    content: '';
                    position: relative;
                    display: block;
                    width: 3px;
                    height: 8px;
                    border-bottom: 2px solid $primaryColor;
                    border-right: 2px solid $primaryColor;
                    transform: rotate(45deg);
                    top: 2px;
                    left: 5px;
                }
            }
        }
    }
}
