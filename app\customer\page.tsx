"use client"

import styles from "./page.module.scss";
import { useEffect, useState, useCallback } from "react";
import { useRouter } from "next/navigation";
import JC_List from "../components/JC_List/JC_List";
import J<PERSON>_Button from "../components/JC_Button/JC_Button";
import JC_Title from "../components/JC_Title/JC_Title";
import JC_Spinner from "../components/JC_Spinner/JC_Spinner";
import { PropertyModel } from "../models/Property";
import { JC_ListHeader } from "../components/JC_List/JC_ListHeader";
import { LocalStorageKeyEnum } from "../enums/LocalStorageKey";

export default function CustomerPage() {
    const router = useRouter();

    // - STATE - //
    const [initialised, setInitialised] = useState<boolean>(false);
    const [properties, setProperties] = useState<PropertyModel[]>([]);
    const [isLoading, setIsLoading] = useState<boolean>(false);

    // - EFFECTS - //
    useEffect(() => {
        loadData();
    }, []);

    // - LOAD DATA - //
    const loadData = useCallback(async () => {
        try {
            setIsLoading(true);

            // Load properties for display
            const propertiesData = await PropertyModel.GetList();
            setProperties(propertiesData || []);
        } catch (error) {
            console.error('Error loading data:', error);
        } finally {
            setIsLoading(false);
            setInitialised(true);
        }
    }, []);

    // - HANDLERS - //
    const handleCustomerClick = (customer: PropertyModel) => {
        // Set the selected customer in localStorage
        localStorage.setItem(LocalStorageKeyEnum.JC_SelectedCustomer, customer.Id);
        router.push(`/customer/edit/${customer.Id}`);
    };

    const handleCreateNew = () => {
        router.push('/customer/edit/new');
    };

    // - LIST CONFIGURATION - //
    const listHeaders: JC_ListHeader[] = [
        { sortKey: "Address", label: "Address" }
    ];

    // - BUILD CUSTOMER LIST - //
    function _buildCustomerList() {
        return (
            <div className={styles.propertyListContainer}>
                <div className={styles.createButtonContainer}>
                    <JC_Button
                        text="Create New Customer"
                        onClick={handleCreateNew}
                        isLoading={isLoading}
                    />
                </div>

                {!isLoading &&
                <JC_List
                    items={properties}
                    headers={listHeaders}
                    defaultSortKey="Address"
                    defaultSortDirection="asc"
                    row={(customer: PropertyModel) => (
                        <tr key={customer.Id} onClick={() => handleCustomerClick(customer)} className={styles.clickableRow}>
                            <td>{customer.Address || 'No address'}</td>
                        </tr>
                    )}
                />}
            </div>
        );
    }

    // - RENDER - //
    if (!initialised) {
        return <JC_Spinner isPageBody />;
    }

    return (
        <div className={styles.mainContainer}>
            <JC_Title title="Customers" />
            {_buildCustomerList()}
        </div>
    );
}
