"use client"

import styles from "./JC_Modal.module.scss";
import React from 'react';
import Image from "next/image";
import { JC_Utils } from "@/app/Utils";
import JC_Title from "../JC_Title/JC_Title";

export default function JC_Modal(_: Readonly<{

    overrideClass?: string;
    width?: string;
    children: React.ReactNode;
    title?: string;
    isOpen: boolean;
    onCancel: () => void;
    transparent?: boolean;

}>) {
    return _.isOpen
        ?
        (
            <React.Fragment>
                <div className={styles.blackOverlay} onClick={_.onCancel}/>
                <div style={_.width ? { width: _.width } : {}} className={`${styles.modalContainer} ${_.transparent ? styles.forceTransparent : ''} ${!JC_Utils.stringNullOrEmpty(_.overrideClass) ? _.overrideClass : ''}`}>
                    {_.title && <JC_Title title={_.title} />}
                    <Image
                        src={`/icons/Cross.webp`}
                        width={0}
                        height={0}
                        className={styles.cancelButton}
                        onClick={_.onCancel}
                        alt="Cancel"
                        unoptimized
                    />
                    <div className={styles.bodyContent}>
                        {_.children}
                    </div>
                </div>
            </React.Fragment>
        )
        :
        null;
}
