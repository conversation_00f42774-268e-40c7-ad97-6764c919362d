@import '../global';

.mainContainer {
    @include mainPageStyles;

    .propertyListContainer {
        display: flex;
        flex-direction: column;
        align-items: center;
        row-gap: 20px;

        .createButtonContainer {
            display: flex;
            justify-content: center;
            margin-bottom: 10px;
        }

        .loadingMessage {
            display: flex;
            align-items: center;
            justify-content: center;
            height: 200px;
            color: $darkGrey;
            font-style: italic;
        }
    }

    .clickableRow {
        cursor: pointer;
        transition: background-color 0.2s ease;

        &:hover {
            background-color: $lightGrey !important;
        }
    }
}

.noCustomerContainer {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 100vh;
    width: 100%;
    background-color: $offWhite;
    gap: 20px;
}


