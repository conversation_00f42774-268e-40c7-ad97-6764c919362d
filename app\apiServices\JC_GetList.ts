import { LocalStorageKeyEnum } from "../enums/LocalStorageKey";
import { JC_Utils } from "../Utils";

const resetIntervalMins = 30;

export async function JC_GetList<T>(routeName:string, params:any, mapper: new (init?: Partial<T>) => T, storageKey?:LocalStorageKeyEnum) : Promise<T[]> {

    let cachedResults = !JC_Utils.stringNullOrEmpty(storageKey) ? localStorage.getItem(storageKey!) : null;
    let nextResetTime = new Date(localStorage.getItem(`${storageKey}_ResetTime`) as string);

    // IF not saved in localStorage yet OR it has been past nextResetTime, get list from backend
    if (cachedResults == null || cachedResults == "" || new Date() > nextResetTime) {
        // Check if we're in demo mode
        const isDemoMode = window.location.pathname.includes('/demo/');

        // Add isDemoMode parameter for ProductGroup and Ingredient routes
        if (routeName === 'productGroup' || routeName === 'ingredient') {
            params.isDemoMode = isDemoMode;
        }

        const response = await fetch(`/api/${routeName}?${new URLSearchParams(params)}`);
        if (!response.ok) { throw new Error(`Failed to fetch ${JC_Utils.routeNameToDescription(routeName)}.`); }
        let resultList:T[] = (await response.json()).result;
        if (!JC_Utils.stringNullOrEmpty(storageKey)) {
            localStorage.setItem(storageKey!, JSON.stringify(resultList));
            localStorage.setItem(`${storageKey}_ResetTime`, new Date(new Date().getTime() + 60000*resetIntervalMins).toString());
        }
        return resultList.map(o => new mapper(o));
    }
    // ELSE return from localStorage
    else {
        return (JSON.parse(cachedResults) as T[]).map(o => new mapper(o));
    }

}