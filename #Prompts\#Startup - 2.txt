= = = = = = = = = = = = = = = =
Environment
= = = = = = = = = = = = = = = =

NAME: AIMS Building Inspection
EMAIL_PRIMARY: <EMAIL>
EMAIL_FROM: <EMAIL>
PORT: 3006

# Recommended for most uses
DATABASE_URL=postgres://neondb_owner:<EMAIL>/neondb?sslmode=require

# For uses requiring a connection without pgbouncer
DATABASE_URL_UNPOOLED=postgresql://neondb_owner:<EMAIL>/neondb?sslmode=require

# Parameters for constructing your own connection string
PGHOST=ep-cold-recipe-a7on7nsc-pooler.ap-southeast-2.aws.neon.tech
PGHOST_UNPOOLED=ep-cold-recipe-a7on7nsc.ap-southeast-2.aws.neon.tech
PGUSER=neondb_owner
PGDATABASE=neondb
PGPASSWORD=npg_Hwt7fPjR9xqg

# Parameters for Vercel Postgres Templates
POSTGRES_URL=postgres://neondb_owner:<EMAIL>/neondb?sslmode=require
POSTGRES_URL_NON_POOLING=postgres://neondb_owner:<EMAIL>/neondb?sslmode=require
POSTGRES_USER=neondb_owner
POSTGRES_HOST=ep-cold-recipe-a7on7nsc-pooler.ap-southeast-2.aws.neon.tech
POSTGRES_PASSWORD=npg_Hwt7fPjR9xqg
POSTGRES_DATABASE=neondb
POSTGRES_URL_NO_SSL=postgres://neondb_owner:<EMAIL>/neondb
POSTGRES_PRISMA_URL=postgres://neondb_owner:<EMAIL>/neondb?connect_timeout=15&sslmode=require

# Neon Auth environment variables for Next.js
NEXT_PUBLIC_STACK_PROJECT_ID=ecc78a20-7ac9-4604-b1b2-cdab7beee94a
NEXT_PUBLIC_STACK_PUBLISHABLE_CLIENT_KEY=pck_9n3br7cs2wg9cq0r2q63awyx8j8w8hj9myzq6w7h18kq0
STACK_SECRET_SERVER_KEY=ssk_9vny216hq10yb7qy68vwknk38d682ygk51mg2wdr06pf8

RESEND_API_KEY: re_NniFvLKN_8AhWGnBH5TZ7dcGnaoLeyCPx


= = = = = = = = = = = = = = = =
Code Replacements
= = = = = = = = = = = = = = = =

SITE_DESCRIPTION: Building Inspection site for AIMS Engineering.

PRIMARY_COLOR: #031363
SECONDARY_COLOR: #539fc1
MISCELLANEOUS_COLOR_1: #F0C31F
MISCELLANEOUS_COLOR_2: #2DB737


= = = = = = = = = = = = = = = =
What You Need To Do
= = = = = = = = = = = = = = = =

In ".env.local", replace these variable values with the values above:
    - Name
    - EMAIL_PRIMARY
    - EMAIL_FROM
    - PORT (also replace the port number in the NEXT_PUBLIC_URL variable)
    - DATABASE_URL
    - DATABASE_URL_UNPOOLED
    - PGHOST
    - PGHOST_UNPOOLED
    - PGUSER
    - PGDATABASE
    - PGPASSWORD
    - POSTGRES_URL
    - POSTGRES_URL_NON_POOLING
    - POSTGRES_USER
    - POSTGRES_HOST
    - POSTGRES_PASSWORD
    - POSTGRES_DATABASE
    - POSTGRES_URL_NO_SSL
    - POSTGRES_PRISMA_URL
    - RESEND_API_KEY
    - NEXT_PUBLIC_STRIPE_PUBLIC_KEY
    - STRIPE_SECRET_KEY
    - PAYMENT_INTENT_SUCCESS_WEBHOOK_SECRET

If these variables are not in the list above, remove them from ".env.local":
    - RESEND_API_KEY
    - NEXT_PUBLIC_STRIPE_PUBLIC_KEY
    - STRIPE_SECRET_KEY
    - PAYMENT_INTENT_SUCCESS_WEBHOOK_SECRET
    
In ".env.local", set AUTH_SECRET to a random string between 35-50 characters long container letters, numbers and symbols.

In "app/layout.tsx", replace "<>SITE_DESCRIPTION<>" with "SITE_DESCRIPTION" value above.

Replace each color in global.scss with the above colors.