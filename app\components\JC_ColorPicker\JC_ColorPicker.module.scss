@import '../../global';

.colorPickerContainer {
    position: absolute;
    top: 40px;
    left: 0;
    z-index: 1000;
    display: flex;
    flex-direction: column;
    background-color: white;
    border: 1px solid $offBlack;
    border-radius: $tinyBorderRadius;
    padding: 10px;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    gap: 10px; // Gap between color history and picker
}

// Color history
.colorHistoryContainer {
    display: grid;
    grid-template-columns: repeat(8, 1fr);
    grid-template-rows: 1fr;
    gap: 5px;
    overflow: visible; // Allow items to be fully visible
    padding: 3px; // Add padding to prevent items from touching the edge
    width: 100%;
}

.colorHistoryItem {
    margin-left: -5px;
    width: 22px;
    height: 22px;
    border-radius: 3px;
    border: 1px solid $offBlack;
    cursor: pointer;
    transition: transform 0.1s ease, outline 0.1s ease;
    position: relative;
    box-sizing: border-box; // Ensure border is included in dimensions
    justify-self: center; // Center the item horizontally in the grid cell

    &:hover {
        transform: scale(1.1);
    }

    &.selected {
        outline: 2px solid $primaryColor;
        outline-offset: 1px;
    }
}

// Color picker content container
.colorPickerContent {
    display: flex;
    flex-direction: column;
}

// React Color Picker wrapper
.reactColorPickerWrapper {
    // Override some of the default styles of ChromePicker
    :global(.chrome-picker) {
        box-shadow: none !important;
        font-family: var(--font-open-sans) !important;
    }
}

// Custom styles for the ChromePicker
.hideFields {
    // Hide the yellow RGB/HSL input fields section
    :global(.chrome-picker > div:last-child > div:last-child) {
        display: none !important;
    }
}

// Set button container
.setButtonContainer {
    display: flex;
    justify-content: center; // Center the button
    margin-top: 7px; // Reduced margin above
    margin-bottom: 8px; // Added margin below
}
