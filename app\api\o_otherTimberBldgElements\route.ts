import { NextRequest, NextResponse } from "next/server";
import { unstable_noStore } from "next/cache";
import { O_OtherTimberBldgElementsModel } from "@/app/models/O_OtherTimberBldgElements";
import { O_OtherTimberBldgElementsBusiness } from "./business";

export const dynamic = 'force-dynamic';

// ------- //
// - GET - //
// ------- //

// Get by "Code" or get all
export async function GET(request: NextRequest) {
    try {
        unstable_noStore();
        const params = new URL(request.url).searchParams;
        const code = params.get("code");
        const getAll = params.get("all") === "true";

        let result;
        if (getAll) {
            result = await O_OtherTimberBldgElementsBusiness.GetList();
        } else if (code) {
            result = await O_OtherTimberBldgElementsBusiness.Get(code);
        } else {
            return NextResponse.json({ error: "Missing 'code' or 'all' parameter" }, { status: 400 });
        }

        return NextResponse.json({ result }, { status: 200 });

    } catch (error) {
        console.log(error);
        return NextResponse.json({ error }, { status: 500 });
    }
}

// ---------- //
// - CREATE - //
// ---------- //

export async function PUT(request: NextRequest) {
    try {
        const params = new URL(request.url).searchParams;
        const isList = params.get("list") === "true";

        if (isList) {
            const dataList: O_OtherTimberBldgElementsModel[] = await request.json();
            await O_OtherTimberBldgElementsBusiness.CreateList(dataList);
        } else {
            const data: O_OtherTimberBldgElementsModel = await request.json();
            await O_OtherTimberBldgElementsBusiness.Create(data);
        }

        return NextResponse.json({ status: 200 });

    } catch (error) {
        console.log(error);
        return NextResponse.json({ error }, { status: 500 });
    }
}

// ---------- //
// - UPDATE - //
// ---------- //

export async function POST(request: NextRequest) {
    try {
        const params = new URL(request.url).searchParams;
        const sortOrderOnly = params.get("sortOrder") === "true";

        if (sortOrderOnly) {
            const { code, sortOrder } = await request.json();
            await O_OtherTimberBldgElementsBusiness.UpdateSortOrder(code, sortOrder);
        } else {
            const data: O_OtherTimberBldgElementsModel = await request.json();
            await O_OtherTimberBldgElementsBusiness.Update(data);
        }

        return NextResponse.json({ status: 200 });

    } catch (error) {
        console.log(error);
        return NextResponse.json({ error }, { status: 500 });
    }
}

// ---------- //
// - DELETE - //
// ---------- //

export async function DELETE(request: NextRequest) {
    try {
        const params = new URL(request.url).searchParams;
        const code = params.get("code");
        const codes = params.get("codes");

        if (codes) {
            const codeList: string[] = codes.split(',');
            await O_OtherTimberBldgElementsBusiness.DeleteList(codeList);
        } else if (code) {
            await O_OtherTimberBldgElementsBusiness.Delete(code);
        } else {
            return NextResponse.json({ error: "Missing 'code' or 'codes' parameter" }, { status: 400 });
        }

        return NextResponse.json({ status: 200 });

    } catch (error) {
        console.log(error);
        return NextResponse.json({ error }, { status: 500 });
    }
}
