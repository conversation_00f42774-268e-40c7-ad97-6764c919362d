SELECT "property"."Id"
      ,"property"."Address"
      ,"property"."BuildingTypeCode"
      ,"buildingType"."Name" "__BuildingType"
      ,"property"."CompanyStrataTitleCode"
      ,"companyStrataTitle"."Name" "__CompanyStrataTitle"
      ,"property"."NumBedroomsCode"
      ,"numBedrooms"."Name" "__NumBedrooms"
      ,"property"."OrientationCode"
      ,"orientation"."Name" "__Orientation"
      ,"property"."StoreysCode"
      ,"storeys"."Name" "__Storeys"
      ,"property"."FurnishedCode"
      ,"furnished"."Name" "__Furnished"
      ,"property"."OccupiedCode"
      ,"occupied"."Name" "__Occupied"
      ,"property"."FloorCode"
      ,"floor"."Name" "__Floor"
      ,"property"."OtherBuildingElementsCode"
      ,"otherBuildingElements"."Name" "__OtherBuildingElements"
      ,"property"."OtherTimberBldgElementsCode"
      ,"otherTimberBldgElements"."Name" "__OtherTimberBldgElements"
      ,"property"."RoofCode"
      ,"roof"."Name" "__Roof"
      ,"property"."WallsCode"
      ,"walls"."Name" "__Walls"
      ,"property"."WeatherCode"
      ,"weather"."Name" "__Weather"
      ,"property"."RoomsListJson"
      ,"property"."SortOrder"
      ,"property"."CreatedAt"
      ,"property"."ModifiedAt"
      ,"property"."Deleted"
FROM public."Property" "property"
LEFT JOIN public."O_BuildingType" "buildingType" ON "property"."BuildingTypeCode" = "buildingType"."Code"
LEFT JOIN public."O_CompanyStrataTitle" "companyStrataTitle" ON "property"."CompanyStrataTitleCode" = "companyStrataTitle"."Code"
LEFT JOIN public."O_NumBedrooms" "numBedrooms" ON "property"."NumBedroomsCode" = "numBedrooms"."Code"
LEFT JOIN public."O_Orientation" "orientation" ON "property"."OrientationCode" = "orientation"."Code"
LEFT JOIN public."O_Storeys" "storeys" ON "property"."StoreysCode" = "storeys"."Code"
LEFT JOIN public."O_Furnished" "furnished" ON "property"."FurnishedCode" = "furnished"."Code"
LEFT JOIN public."O_Occupied" "occupied" ON "property"."OccupiedCode" = "occupied"."Code"
LEFT JOIN public."O_Floor" "floor" ON "property"."FloorCode" = "floor"."Code"
LEFT JOIN public."O_OtherBuildingElements" "otherBuildingElements" ON "property"."OtherBuildingElementsCode" = "otherBuildingElements"."Code"
LEFT JOIN public."O_OtherTimberBldgElements" "otherTimberBldgElements" ON "property"."OtherTimberBldgElementsCode" = "otherTimberBldgElements"."Code"
LEFT JOIN public."O_Roof" "roof" ON "property"."RoofCode" = "roof"."Code"
LEFT JOIN public."O_Walls" "walls" ON "property"."WallsCode" = "walls"."Code"
LEFT JOIN public."O_Weather" "weather" ON "property"."WeatherCode" = "weather"."Code"
WHERE 1=1
      AND "property"."Deleted" = 'False'
ORDER BY "property"."SortOrder", "property"."Address";
