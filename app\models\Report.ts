import { JC_Get } from "../apiServices/JC_Get";
import { JC_GetList } from "../apiServices/JC_GetList";
import { JC_Post } from "../apiServices/JC_Post";
import { JC_Put } from "../apiServices/JC_Put";
import { JC_Delete } from "../apiServices/JC_Delete";
import { JC_Utils } from "../Utils";
import { _Base } from "./_Base";

export class ReportModel extends _Base {

    // - -------- - //
    // - SERVICES - //
    // - -------- - //

    static apiRoute:string = "report";
    static async Get(id: string) {
        return await JC_Get<ReportModel>(this.apiRoute, { id }, ReportModel);
    }
    static async GetList() {
        return await JC_GetList<ReportModel>(`${this.apiRoute}/getList`, {}, ReportModel);
    }
    static async Create(data: ReportModel) {
        return await JC_Put<ReportModel>(this.apiRoute, data);
    }
    static async CreateList(dataList: ReportModel[]) {
        return await JC_Put<ReportModel[]>(`${this.apiRoute}/createList`, dataList);
    }
    static async Update(data: ReportModel) {
        return await JC_Post<ReportModel>(this.apiRoute, data);
    }
    static async UpdateList(dataList: ReportModel[]) {
        return await JC_Post<ReportModel[]>(`${this.apiRoute}/updateList`, dataList);
    }
    static async Delete(id: string) {
        return await JC_Delete(this.apiRoute, id);
    }
    static async DeleteList(ids: string[]) {
        return await JC_Post(`${this.apiRoute}/deleteList`, { ids });
    }

    // - --------- - //
    // - VARIABLES - //
    // - --------- - //

    Id: string;
    PropertyId: string;
    UserId: string;
    Name: string;
    PostalAddress: string;
    ClientName: string;
    ClientPhone: string;
    ClientEmail: string;
    ClientPrincipalName: string;
    InspectionDate: Date;
    InspectorNameOverride?: string;
    InspectorPhoneOverride?: string;
    InspectorQualificationOverride?: string;
    FileId: string;

    // - ----------- - //
    // - CONSTRUCTOR - //
    // - ----------- - //

    constructor(init?: Partial<ReportModel>) {
        super(init);
        this.Id = JC_Utils.generateGuid();
        this.PropertyId = "";
        this.UserId = "";
        this.Name = "";
        this.PostalAddress = "";
        this.ClientName = "";
        this.ClientPhone = "";
        this.ClientEmail = "";
        this.ClientPrincipalName = "";
        this.InspectionDate = new Date();
        this.InspectorNameOverride = undefined;
        this.InspectorPhoneOverride = undefined;
        this.InspectorQualificationOverride = undefined;
        this.FileId = "";
        Object.assign(this, init);
    }

    // - ------ - //
    // - STRING - //
    // - ------ - //

    toString() {
        return `${this.Name} | ${this.ClientName}`;
    }
}
