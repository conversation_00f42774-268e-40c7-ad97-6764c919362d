"use client"

import styles from "./page.module.scss";
import { useEffect, useState } from "react";
import { useRouter } from "next/navigation";
import <PERSON><PERSON>_<PERSON>ton from "../components/JC_Button/JC_Button";
import JC_Title from "../components/JC_Title/JC_Title";
import JC_Spinner from "../components/JC_Spinner/JC_Spinner";
import { PropertyModel } from "../models/Property";
import { LocalStorageKeyEnum } from "../enums/LocalStorageKey";

export default function PropertyPage() {
    const router = useRouter();

    // - STATE - //
    const [initialised, setInitialised] = useState<boolean>(false);
    const [noCustomerSelected, setNoCustomerSelected] = useState<boolean>(false);

    // - EFFECTS - //
    useEffect(() => {
        checkForSelectedCustomer();
        setInitialised(true);
    }, []);

    // Check if there's a selected customer in localStorage
    const checkForSelectedCustomer = async () => {
        try {
            const selectedCustomerId = localStorage.getItem(LocalStorageKeyEnum.JC_SelectedCustomer);

            if (!selectedCustomerId) {
                setNoCustomerSelected(true);
                return;
            }

            // Check if this customer exists and is not deleted
            const customer = await PropertyModel.Get(selectedCustomerId);

            if (customer && !customer.Deleted) {
                // Redirect to property edit page for this customer
                router.push(`/property/edit/${selectedCustomerId}`);
                return;
            } else {
                // Customer doesn't exist or is deleted, clear localStorage
                localStorage.removeItem(LocalStorageKeyEnum.JC_SelectedCustomer);
                setNoCustomerSelected(true);
            }
        } catch (error) {
            console.error('Error checking selected customer:', error);
            // Clear localStorage if there's an error
            localStorage.removeItem(LocalStorageKeyEnum.JC_SelectedCustomer);
            setNoCustomerSelected(true);
        }
    };

    // Handle Customers button click
    const handleCustomersClick = () => {
        router.push('/customer');
    };

    // - RENDER - //
    if (!initialised) {
        return <JC_Spinner isPageBody />;
    }

    if (noCustomerSelected) {
        return (
            <div className={styles.noCustomerContainer}>
                <JC_Title title="Select a Customer" />
                <JC_Button
                    text="Customers"
                    onClick={handleCustomersClick}
                />
            </div>
        );
    }

    // If we reach here, we're still loading/checking for customer
    return <JC_Spinner isPageBody />;
}
