import { sql } from "@vercel/postgres";
import { O_BuildingTypeModel } from "@/app/models/O_BuildingType";

export class O_BuildingTypeBusiness {

    // - --- - //
    // - GET - //
    // - --- - //

    static async Get(code: string) {
        return (await sql<O_BuildingTypeModel>`
            SELECT "Code",
                   "Name",
                   "SortOrder",
                   "CreatedAt",
                   "ModifiedAt",
                   "Deleted"
            FROM public."O_BuildingType"
            WHERE "Code" = ${code}
              AND "Deleted" = 'False'
        `).rows[0];
    }

    static async GetList() {
        return (await sql<O_BuildingTypeModel>`
            SELECT "Code",
                   "Name",
                   "SortOrder",
                   "CreatedAt",
                   "ModifiedAt",
                   "Deleted"
            FROM public."O_BuildingType"
            WHERE "Deleted" = 'False'
            ORDER BY "SortOrder", "Name"
        `).rows;
    }

    // - ------ - //
    // - CREATE - //
    // - ------ - //

    static async Create(data: O_BuildingTypeModel) {
        await sql`
            INSERT INTO public."O_BuildingType"
            ("Code", "Name", "SortOrder", "CreatedAt")
            VALUES
            (${data.Code}, ${data.Name}, ${data.SortOrder}, ${new Date().toUTCString()})
        `;
    }

    static async CreateList(dataList: O_BuildingTypeModel[]) {
        for (const data of dataList) {
            await this.Create(data);
        }
    }

    // - ------ - //
    // - UPDATE - //
    // - ------ - //

    static async Update(data: O_BuildingTypeModel) {
        await sql`
            UPDATE public."O_BuildingType"
            SET "Name" = ${data.Name},
                "SortOrder" = ${data.SortOrder},
                "ModifiedAt" = ${new Date().toUTCString()},
                "Deleted" = ${data.Deleted}
            WHERE "Code" = ${data.Code}
        `;
    }

    static async UpdateList(dataList: O_BuildingTypeModel[]) {
        for (const data of dataList) {
            await this.Update(data);
        }
    }

    static async UpdateSortOrder(code: string, sortOrder: number) {
        await sql`
            UPDATE public."O_BuildingType"
            SET "SortOrder" = ${sortOrder},
                "ModifiedAt" = ${new Date().toUTCString()}
            WHERE "Code" = ${code}
        `;
    }

    // - ------ - //
    // - DELETE - //
    // - ------ - //

    static async Delete(code: string) {
        await sql`
            UPDATE public."O_BuildingType"
            SET "Deleted" = 'True',
                "ModifiedAt" = ${new Date().toUTCString()}
            WHERE "Code" = ${code}
        `;
    }

    static async DeleteList(codes: string[]) {
        for (const code of codes) {
            await this.Delete(code);
        }
    }
}
