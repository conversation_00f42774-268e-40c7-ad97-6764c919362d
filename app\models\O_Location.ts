import { JC_Get } from "../apiServices/JC_Get";
import { JC_GetList } from "../apiServices/JC_GetList";
import { JC_Post } from "../apiServices/JC_Post";
import { JC_Put } from "../apiServices/JC_Put";
import { JC_Delete } from "../apiServices/JC_Delete";
import { _Base } from "./_Base";

export class O_LocationModel extends _Base {

    // - -------- - //
    // - SERVICES - //
    // - -------- - //

    static apiRoute:string = "o_location";
    static async Get(code: string) {
        return await JC_Get<O_LocationModel>(this.apiRoute, { code }, O_LocationModel);
    }
    static async GetList() {
        return await JC_GetList<O_LocationModel>(`${this.apiRoute}/getList`, {}, O_LocationModel);
    }
    static async Create(data: O_LocationModel) {
        return await JC_Put<O_LocationModel>(this.apiRoute, data);
    }
    static async CreateList(dataList: O_LocationModel[]) {
        return await JC_Put<O_LocationModel[]>(`${this.apiRoute}/createList`, dataList);
    }
    static async Update(data: O_LocationModel) {
        return await JC_Post<O_LocationModel>(this.apiRoute, data);
    }
    static async UpdateList(dataList: O_LocationModel[]) {
        return await JC_Post<O_LocationModel[]>(`${this.apiRoute}/updateList`, dataList);
    }
    static async Delete(code: string) {
        return await JC_Delete(this.apiRoute, code);
    }
    static async DeleteList(codes: string[]) {
        return await JC_Post(`${this.apiRoute}/deleteList`, { codes });
    }

    // - --------- - //
    // - VARIABLES - //
    // - --------- - //

    Code: string;
    Name: string;
    SortOrder: number;

    // - ----------- - //
    // - CONSTRUCTOR - //
    // - ----------- - //

    constructor(init?: Partial<O_LocationModel>) {
        super(init);
        this.Code = "";
        this.Name = "";
        this.SortOrder = 999;
        Object.assign(this, init);
    }

    // - ------ - //
    // - STRING - //
    // - ------ - //

    toString() {
        return `${this.Code} | ${this.Name}`;
    }
}
