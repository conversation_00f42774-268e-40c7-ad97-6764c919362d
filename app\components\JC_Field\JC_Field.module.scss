@import '../../global';

$leftGap: 10px;
$normalInputWidth: 350px;

.mainContainer {

    input[disabled] { pointer-events:none } // So parent clicks trigger properly

    // Label
    .label {
        margin-bottom: 6px;
        padding-left: 11px;
        text-align: left;
        font-size: $defaultFontSize;
        font-weight: bold;

        &.numberStepperLabel, &.numberLabel {
            padding-left: 0;
            text-align: center;
        }

        // Error Message
        .errorSpan {
            color: $errorColor;
            font-weight: bold;
            padding-left: 10px;
            position: absolute;
            // opacity: 0;
            // animation: errorFadeOutAnimation 2.5s;
        }
    }

    // Input
    .inputContainer {
        outline: solid $tinyBorderWidth $offBlack;
        border-radius: $tinyBorderRadius;
        margin-top: 2px;
        width: $normalInputWidth;
        max-width: 100%;
        height: 40px;
        background-color: $offWhite;

        // Input
        input,
        textarea {
            width: 100%;
            height: 100%;
            padding-left: 10px;
            box-sizing: border-box;
            outline: none !important;
            border: none !important;
            background-color: transparent;
            font-size: $defaultFontSize;
            resize: none;
        }
        textarea {
            padding-top: 8px;
        }

        // Read-Only
        &.readOnly {
            background-color: $grey !important;
            outline-color: $grey !important;
        }

        // Type: Text
        &.textType {
            display: block;
        }

        // Type: Number
        &.numberType {
            width: 70px;
        }

        // Type: NumberStepper
        &.numberStepper {
            width: max-content;
            position: relative;
            background-color: transparent;
            outline: none;
            margin: 0 auto; /* Center the NumberStepper */

            .inputContainer {
                width: 70px;
                height: 100%;
                border-radius: $tinyBorderRadius;
                outline: solid $tinyBorderWidth $offBlack;
                overflow: hidden;
                margin: 0 auto; /* Center the input */

                input {
                    width: 100%;
                    height: 100%;
                    text-align: center;
                    background-color: $offWhite;
                    padding-left: 0;
                    box-sizing: border-box;
                    outline: none !important;
                    border: none !important;
                    font-size: $defaultFontSize;
                }
            }

            .stepperButtons {
                height: 100%;
                width: 20px;
                display: flex;
                flex-direction: column;
                border-radius: $tinyBorderRadius;
                outline: solid $tinyBorderWidth $offBlack;
                overflow: hidden;
                position: absolute;
                right: -28px;
                top: 0;

                .stepButton {
                    height: 50%;
                    width: 100%;
                    display: flex;
                    justify-content: center;
                    align-items: center;
                    cursor: pointer;
                    user-select: none;
                    background-color: $primaryColor;
                    color: white;
                    transition: background-color 0.2s;
                    margin: 0;
                    padding: 0;
                    box-sizing: border-box;

                    &:hover {
                        background-color: darken($primaryColor, 10%);
                    }

                    &.upButton {
                        border-bottom: solid $tinyBorderWidth $offBlack;
                    }
                }
            }
        }

        // Type: NumberStepperArrowsOnly
        &.numberStepperArrowsOnly {
            width: 20px;
            position: relative;
            background-color: transparent;
            outline: none;
            margin: 0 auto;
            height: 40px;
            padding-left: 11px; /* Match the label's padding-left */

            .stepperButtons {
                height: 100%;
                width: 20px;
                display: flex;
                flex-direction: column;
                border-radius: $tinyBorderRadius;
                outline: solid $tinyBorderWidth $offBlack;
                overflow: hidden;
                position: relative;

                .stepButton {
                    height: 50%;
                    width: 100%;
                    display: flex;
                    justify-content: center;
                    align-items: center;
                    cursor: pointer;
                    user-select: none;
                    background-color: $primaryColor;
                    color: white;
                    transition: background-color 0.2s;
                    margin: 0;
                    padding: 0;
                    box-sizing: border-box;

                    &:hover {
                        background-color: darken($primaryColor, 10%);
                    }

                    &.upButton {
                        border-bottom: solid $tinyBorderWidth $offBlack;
                    }
                }
            }
        }

        // Type: Password
        &.passwordType {
            display: block;
        }

        // Type: Textarea
        &.textareaType  {
            width: $normalInputWidth;
            height: 130px;
        }

        // Type: Dropdown
        &.dropdownType {
            display: block;

            .dropdown {
                width: 100%;
                height: 100%;
                padding-left: 8px;
                box-sizing: border-box;
                outline: none !important;
                border: none !important;
                background-color: transparent;
                font-size: $defaultFontSize;
                color: $offBlack;
                appearance: none;
                background-image: url('/icons/Chevron.webp');
                background-repeat: no-repeat;
                background-position: right 10px center;
                background-size: 10px;
                cursor: pointer;

                option {
                    background-color: $offBlack;
                    color: $secondaryColor;
                    padding: 8px;
                }
            }
        }

        // Type: Radio
        &.radioType {
            display: block;
            padding: 0;
            border: none;
            background-color: transparent;
            box-shadow: none;
            outline: none;
            height: max-content;
        }

        // Type: Rich Text
        &.richTextType  {
            width: 280px;
            height: 160px;
            font-size: 15px;
            display: flex;
            flex-direction: column;
            // Buttons
            .richTextButtons {
                width: 100%;
                display: flex;
                column-gap: 10px;
                box-sizing: border-box;
                padding: 8px;
                border-bottom: solid $smallBorderWidth $offBlack;
                button {
                    padding: 5px 10px 5px 9px;
                    width: 30px;
                    height: 29px;
                    background-color: transparent;
                    border-radius: $tinyBorderRadius;
                    cursor: pointer;
                }
                // Color picker wrapper
                .colorPickerWrapper {
                    position: relative;
                }

                // Color button
                .colorButton {
                    width: 30px;
                    height: 29px;
                    border-radius: $tinyBorderRadius;
                    cursor: pointer;
                    padding: 0;
                    border: 1px solid $offBlack;

                    // Remove default color input styling
                    &::-webkit-color-swatch-wrapper {
                        padding: 0;
                    }

                    &::-webkit-color-swatch {
                        border: none;
                        border-radius: $tinyBorderRadius;
                    }
                }
                button.isActive {
                    background-color: $secondaryColor;
                }
            }
            > div:last-child {
                overflow-y: auto;
                flex-grow: 1;
                background-color: $offWhite;
                > div {
                    height: 100%;
                    padding: 10px;
                    box-sizing: border-box;
                    outline: none !important;
                    > p {
                        margin: 0;
                    }
                }
            }
        }
    }

}

// Error Fade Out Animation
@keyframes errorFadeOutAnimation {
    0%   { opacity: 1; }
    80%  { opacity: 1; }
    100% { opacity: 0; }
}