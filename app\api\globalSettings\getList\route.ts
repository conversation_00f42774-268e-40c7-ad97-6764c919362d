import { NextResponse } from "next/server";
import { unstable_noStore } from "next/cache";
import { GlobalSettingsBusiness } from "../business";

export const dynamic = 'force-dynamic';

// Get all GlobalSettings
export async function GET() {
    try {
        unstable_noStore();
        const result = await GlobalSettingsBusiness.GetList();
        return NextResponse.json({ result }, { status: 200 });
    } catch (error) {
        console.log(error);
        return NextResponse.json({ error }, { status: 500 });
    }
}
