import { JC_Utils } from "../Utils";

const resetIntervalMins = 30;

export async function JC_Post<T>(routeName:string, body:T, params?:any) {
    const response = await fetch(`/api/${routeName}${params != null ? `?${new URLSearchParams(params)}` : ""}`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(body)
    });
    if (!response.ok) { throw new Error(`Failed to update ${JC_Utils.routeNameToDescription(routeName)}.`); }
    return (await response.json());
}