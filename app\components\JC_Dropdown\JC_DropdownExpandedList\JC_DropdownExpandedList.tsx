"use client"

import styles from "./JC_DropdownExpandedList.module.scss";
import React, { useEffect, useRef, useState } from 'react';
import { JC_FieldOption } from '@/app/models/ComponentModels/JC_FieldOption';
import { DropdownTypeEnum } from '@/app/enums/DropdownType';

interface JC_DropdownExpandedListProps {
    options: JC_FieldOption[];
    selectedOptionId?: string;
    type: DropdownTypeEnum;
    position: { top: number, left: number, width: number };
    onOptionMouseOver?: (optionId: string) => void;
    onOptionMouseOut?: (optionId: string) => void;
    onSelection: (newOptionId: string) => void;
    buildOptionContent: (option: JC_FieldOption, isMain?: boolean) => React.ReactNode;
}

export default function JC_DropdownExpandedList({
    options,
    selectedOptionId,
    type,
    position,
    onOptionMouseOver,
    onOptionMouseOut,
    onSelection,
    buildOptionContent
}: JC_DropdownExpandedListProps) {
    const containerRef = useRef<HTMLDivElement>(null);
    const lastItemRef = useRef<HTMLDivElement>(null);

    // Max height is defined in SCSS as $height

    function isOptionSelected(optionId: string) {
        if (!selectedOptionId) return false;
        return type === DropdownTypeEnum.Default && optionId === selectedOptionId;
    }

    // Function removed as we're now calling onSelection directly from the onClick handler

    // No need to calculate container height as it's handled by CSS

    return (
        <>
            {/* Main dropdown container */}
            <div
                className={styles.dropdownPortal}
                style={{
                    top: `${position.top}px`,
                    left: `${position.left}px`,
                    width: `${position.width}px`,
                }}
            >
                <div ref={containerRef} className={styles.scrollContainer}>
                    {options.map((option, index) => (
                        <div
                            key={option.OptionId}
                            ref={index === options.length - 1 ? lastItemRef : null}
                            className={`${styles.dropdownOption} ${isOptionSelected(option.OptionId) ? styles.selected : ''}`}
                            onClick={(e) => {
                                e.stopPropagation(); // Stop event from bubbling up
                                console.log("JC_DropdownExpandedList - DIV clicked:", option.OptionId, option.Label);
                                // Call onSelection directly
                                onSelection(option.OptionId);
                                // Prevent default to avoid any browser behavior
                                e.preventDefault();
                            }}
                            onMouseOver={(e) => {
                                if (!isOptionSelected(option.OptionId)) {
                                    e.currentTarget.style.backgroundColor = '#dedede';
                                }
                                if (onOptionMouseOver) {
                                    onOptionMouseOver(option.OptionId);
                                }
                            }}
                            onMouseOut={(e) => {
                                if (!isOptionSelected(option.OptionId)) {
                                    e.currentTarget.style.backgroundColor = '#F8F8F8';
                                }
                                if (onOptionMouseOut) {
                                    onOptionMouseOut(option.OptionId);
                                }
                            }}
                        >
                            {buildOptionContent(option)}
                        </div>
                    ))}
                </div>
            </div>
        </>
    );
}
