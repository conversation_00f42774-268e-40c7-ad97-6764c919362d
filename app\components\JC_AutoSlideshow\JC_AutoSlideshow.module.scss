@import '../../global';

$fadeDuration: 1.5s;

.mainContainer {
    position: relative;
    width: 100%;
    height: 100%;

    // Image
    .image {
        position: absolute;
        width: 100%;
        height: 100%;
        object-fit: cover;
        opacity: 0;
        

        &.fadingOut {
            opacity: 1;
            animation: fadeOutAnimation $fadeDuration;
        }

        &.fadingIn {
            opacity: 1;
            animation: fadeInAnimation $fadeDuration;
        }
    }

    // Darken
    .darkenImage {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background-color: black;
        z-index: 998;
    }
}


// - Animations - //

@keyframes fadeOutAnimation {
    0%   {  opacity: 1;   }
    100% {  opacity: 0; }
}

@keyframes fadeInAnimation {
    0%   {  opacity: 0;   }
    100% {  opacity: 1; }
}