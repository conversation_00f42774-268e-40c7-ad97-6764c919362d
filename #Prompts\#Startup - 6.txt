= = = = = = = = = = = = = = = =
Pages
= = = = = = = = = = = = = = = =

"Customer" page:
    - Left pane is scrollable list of Reports, each showing it's Name
        - IF the user has "IsAdmin" true, show full list of reports , otherwise show only the reports tied to that User (by UserId, the getList for Reports should take in a nullable userId param)
    - Right pane is a JC_Form with these fields:
        - Report Type (free text)
        - Client Name (free text)
        - Client Phone (free text)
        - Client Email (free text)
        - Client Principal Name (free text)
        - Property Address (read-only, comes from Property object)
        - Postal Address (free text)
        - Inspection Date (date picker)
        - Inspector Name Override (free text, if logged-in User has "IsAdmin" false then this is read-only and the value is the logged-in User's FirstName+LastName)
        - Inspector Phone Override (free text, if logged-in User has "IsAdmin" false then this is read-only and the value is the logged-in User's Phone)
        - Inspector Qualification Override (free text, if logged-in User has "IsAdmin" false then this is read-only and the value is the logged-in User's Qualification)

"Property" page:
    - Left side is scrollable pane with JC_Form:
        - Building Type dropdown (options come from BuildingType table)
        - "Company or Strata Title" (free text)
        - No. of Bedrooms (number, 0db)
        - Orientation (options come from Orientation table)
        - Storeys (number, 0db)
        - Furnished (checkbox)
        - Occupied (checkbox)
        - Floor (number, 0db)
        - Rooms (multi-select dropdown, options come from Room table, selected values are stored in Property's RoomsJson)

"Defects" page:
    - Left side is scrollable list with list of Rooms each showing it's Name (given by Property's RoomsJson)
    - Right side is JC_List with these columns:
        - Building (from BuildingType)
        - Area (from Area)
        - Location (from Location)
        - Orientation (from Orientation)
        - Defects (from Defects)
        - Serverity (from Serverity)
        - Images (this is the total number of DefectImage's tied to this Defect)


= = = = = = = = = = = = = = = =
What You Need To Do
= = = = = = = = = = = = = = = =

Implement all the front-end and back-end for the above.

For every change that is made in this project, make sure you:
    - Use existing components as much as you can, it is very important that you do not create any new ones, just edit the existing components for my requirements.
    - Use global.scss variables wherever can.
    - Follow existing patterns as much as possible
    - Make sure any utils functions created are put into Utils.ts in a new or existing class.

Then, fix all build errors. Keep building until all errors are fixed, making sure you follow the above requirements.