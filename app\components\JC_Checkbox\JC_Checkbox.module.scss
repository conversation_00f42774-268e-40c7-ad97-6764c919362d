@import '../../global';

$size: 13px;
$innerGap: 3px;

.mainContainer {
    width: max-content;
    display: flex;
    align-items: center;
    column-gap: 12px;
    user-select: none;
    &.clickable {
        &, .checkbox, label {
            cursor: pointer !important;
        }
    }

    // Read-Only state
    &.readOnly {
        opacity: 0.7;
        cursor: default;

        .checkbox, label {
            cursor: default;
        }
    }

    .checkbox {
        width: $size;
        height: $size;
        outline: solid $tinyBorderWidth $primaryColor;
        border-radius: 1px;
        position: relative;

        .innerCheckedSquare {
            position: absolute;
            margin: $innerGap;
            z-index: 999;
            width: calc($size - 2 * $innerGap);
            height: calc($size - 2 * $innerGap);
            border-radius: 1px;
            background-color: $primaryColor;
        }
    }
}