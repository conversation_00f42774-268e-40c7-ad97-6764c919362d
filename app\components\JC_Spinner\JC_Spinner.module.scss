@import '../../global';

$spinnerSize: 40px;
$spinnerBorderWidth: 4px;

.spinnerContainer {
    width: 100%;
    height: 100%;
    position: relative;
    user-select: none;

    &.small {
        transform: scale(0.4);
    }
}

.spinner {
    width: $spinnerSize;
    height: $spinnerSize;
    position: absolute;
    left: 50%;
    top: 50%;
    transform: translate(-50%, -50%);
    border: $spinnerBorderWidth solid rgba($primaryColor, 0.2);
    border-top: $spinnerBorderWidth solid $primaryColor;
    border-radius: 50%;
    animation: fadeInAnimation 0.4s ease-out,
               spinAnimation 1.3s infinite linear;
}

/* Fade In */

@keyframes fadeInAnimation {
    0%   { opacity: 0; }
    100% { opacity: 1; }
}

/* Spin */

@keyframes spinAnimation {
    0%   { transform: translate(-50%, -50%) rotate(0deg); }
    100% { transform: translate(-50%, -50%) rotate(360deg); }
}