import { JC_Utils } from "../Utils";

export async function JC_Delete(routeName:string, id:string) : Promise<boolean> {
    const response = await fetch(`/api/${routeName}?${new URLSearchParams({id})}`, {
        method: 'DELETE',
        headers: { 'Content-Type': 'application/json' }
    });
    if (!response.ok) { throw new Error(`Failed to delete ${JC_Utils.routeNameToDescription(routeName)} with Id "${id}".`); };
    return true;
}