Make sure every model has a corresponding Get api route.

Make sure every model has a corresponding GetList api sub-route.

Make sure every model has a corresponding Create api route.

Make sure every model has a corresponding CreateList api sub-route.

Make sure every model has a corresponding Update api route.

Make sure every model has a corresponding UpdateList api sub-route.

Make sure every model has a corresponding Delete api route.

Make sure every model has a corresponding DeleteList api sub-route.

Make sure every model that has a "SortOrder" field has a corresponding updateSortOrder api sub-route.

For every model, double check it has these corresponding routes:
    - Get
    - GetList sub-route
    - Create
    - CreateList sub-route
    - Update
    - UpdateList sub-route
    - Delete
    - DeleteList sub-route

Fix all build errors. Keep building until all errors are fixed, making sure you follow the above requirements.