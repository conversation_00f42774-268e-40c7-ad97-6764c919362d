import { NextRequest, NextResponse } from "next/server";
import { O_StoreysBusiness } from "../business";

export async function DELETE(request: NextRequest) {
    try {
        const { codes } = await request.json();
        if (!codes || !Array.isArray(codes)) {
            return NextResponse.json({ error: "Missing or invalid 'codes' array in request body" }, { status: 400 });
        }
        await O_StoreysBusiness.DeleteList(codes);
        return NextResponse.json({ status: 200 });
    } catch (error) {
        console.log(error);
        return NextResponse.json({ error }, { status: 500 });
    }
}
