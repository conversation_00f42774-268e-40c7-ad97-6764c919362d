import { JC_Get } from "../apiServices/JC_Get";
import { JC_GetList } from "../apiServices/JC_GetList";
import { JC_Post } from "../apiServices/JC_Post";
import { JC_Put } from "../apiServices/JC_Put";
import { JC_Delete } from "../apiServices/JC_Delete";
import { JC_Utils } from "../Utils";
import { _Base } from "./_Base";

export class DefectImageModel extends _Base {

    // - -------- - //
    // - SERVICES - //
    // - -------- - //

    static apiRoute:string = "defectImage";
    static async Get(id: string) {
        return await JC_Get<DefectImageModel>(this.apiRoute, { id }, DefectImageModel);
    }
    static async GetList() {
        return await JC_GetList<DefectImageModel>(`${this.apiRoute}/getList`, {}, DefectImageModel);
    }
    static async Create(data: DefectImageModel) {
        return await JC_Put<DefectImageModel>(this.apiRoute, data);
    }
    static async CreateList(dataList: DefectImageModel[]) {
        return await JC_Put<DefectImageModel[]>(`${this.apiRoute}/createList`, dataList);
    }
    static async Update(data: DefectImageModel) {
        return await JC_Post<DefectImageModel>(this.apiRoute, data);
    }
    static async UpdateList(dataList: DefectImageModel[]) {
        return await JC_Post<DefectImageModel[]>(`${this.apiRoute}/updateList`, dataList);
    }
    static async Delete(id: string) {
        return await JC_Delete(this.apiRoute, id);
    }
    static async DeleteList(ids: string[]) {
        return await JC_Post(`${this.apiRoute}/deleteList`, { ids });
    }

    // - --------- - //
    // - VARIABLES - //
    // - --------- - //

    Id: string;
    DefectId: string;
    ImageName: string;
    ImageFileId: string;

    // - ----------- - //
    // - CONSTRUCTOR - //
    // - ----------- - //

    constructor(init?: Partial<DefectImageModel>) {
        super(init);
        this.Id = JC_Utils.generateGuid();
        this.DefectId = "";
        this.ImageName = "";
        this.ImageFileId = "";
        Object.assign(this, init);
    }

    // - ------ - //
    // - STRING - //
    // - ------ - //

    toString() {
        return `${this.ImageName}`;
    }
}
