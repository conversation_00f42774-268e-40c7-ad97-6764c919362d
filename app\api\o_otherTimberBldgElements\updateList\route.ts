import { NextRequest, NextResponse } from "next/server";
import { O_OtherTimberBldgElementsModel } from "@/app/models/O_OtherTimberBldgElements";
import { O_OtherTimberBldgElementsBusiness } from "../business";

export async function POST(request: NextRequest) {
    try {
        const dataList: O_OtherTimberBldgElementsModel[] = await request.json();
        await O_OtherTimberBldgElementsBusiness.UpdateList(dataList);
        return NextResponse.json({ status: 200 });
    } catch (error) {
        console.log(error);
        return NextResponse.json({ error }, { status: 500 });
    }
}
