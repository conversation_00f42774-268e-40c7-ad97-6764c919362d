import { NextRequest, NextResponse } from "next/server";
import { unstable_noStore } from "next/cache";
import { ReportBusiness } from "../business";

export const dynamic = 'force-dynamic';

// Get Reports by PropertyId
export async function GET(request: NextRequest) {
    try {
        unstable_noStore();
        const params = new URL(request.url).searchParams;
        const propertyId = params.get("propertyId");
        
        if (!propertyId) {
            return NextResponse.json({ error: "Missing 'propertyId' parameter" }, { status: 400 });
        }

        const result = await ReportBusiness.GetByPropertyId(propertyId);
        return NextResponse.json({ result }, { status: 200 });
    } catch (error) {
        console.log(error);
        return NextResponse.json({ error }, { status: 500 });
    }
}
