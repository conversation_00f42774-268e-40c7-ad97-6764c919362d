@import '../../global';

.mainContainer {
    position: relative;
    display: inline-block;

    &.absoluteFill {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        z-index: 10;
    }

    .tooltip {
        opacity: 0;
        position: absolute;
        width: max-content;
        height: max-content;
        font-size: 12px;
        font-weight: normal;
        background-color: $offBlack;
        color: $white;
        border: 2px solid $primaryColor;
        border-radius: $tinyBorderRadius;
        white-space: pre-line;
        text-align: center;
        @include containerShadow;
        transition: opacity 0.2s ease, margin-bottom 0.2s ease, visibility 0.2s;
        // Position it 10px above the element initially
        bottom: calc(100% + 10px);
        left: 50%;
        margin-bottom: -15px;
        z-index: 100;
        pointer-events: none; // Make tooltip non-hoverable
    }

    &:hover {
        .tooltip {
            opacity: 1;
            visibility: visible;
            margin-bottom: 0px;
        }
    }
}