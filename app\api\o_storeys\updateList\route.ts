import { NextRequest, NextResponse } from "next/server";
import { O_StoreysModel } from "@/app/models/O_Storeys";
import { O_StoreysBusiness } from "../business";

export async function POST(request: NextRequest) {
    try {
        const dataList: O_StoreysModel[] = await request.json();
        await O_StoreysBusiness.UpdateList(dataList);
        return NextResponse.json({ status: 200 });
    } catch (error) {
        console.log(error);
        return NextResponse.json({ error }, { status: 500 });
    }
}
