import { Resend } from "resend";
import Template_ContactEmail from '@/templates/email/Contact';
import Template_ResetPassswordEmail from "@/templates/email/ResetPassword";
import Template_UserVerificationEmail from "@/templates/email/UserVerification";
import Template_WelcomeEmail from "@/templates/email/Welcome";
import { ContactModel } from "@/app/models/Contact";

export class EmailBusiness {

    // Contact
    static async SendContactEmail(emailData:ContactModel) {
        const resend = new Resend(process.env.RESEND_API_KEY);
        await resend.emails.send({
            from: `${process.env.NAME} ${process.env.EMAIL_FROM}`,
            to: '<EMAIL>',
            subject: 'TEST MATE',
            react: Template_ContactEmail(emailData)
        });
    }

    // Welcome
    static async SendWelcomeEmail(name:string, email:string) {
        const resend = new Resend(process.env.RESEND_API_KEY);
        await resend.emails.send({
            from: `${process.env.NAME} ${process.env.EMAIL_FROM}`,
            to: email,
            subject: 'TEST MATE',
            react: Template_WelcomeEmail(name, email)
        });
    }



    // Reset Password
    static async SendResetPasswordEmail(email:string, theToken:string) {
        const resend = new Resend(process.env.RESEND_API_KEY);
        await resend.emails.send({
            from: `${process.env.NAME} ${process.env.EMAIL_FROM}`,
            to: email,
            subject: `${process.env.NAME} Reset Password`,
            react: Template_ResetPassswordEmail({ token: theToken })
        });
    }

    // User Verification
    static async SendUserVerificationEmail(email:string, theToken:string) {
        const resend = new Resend(process.env.RESEND_API_KEY);
        await resend.emails.send({
            from: `${process.env.NAME} ${process.env.EMAIL_FROM}`,
            to: email,
            subject: `${process.env.NAME} Verification`,
            react: Template_UserVerificationEmail({ token: theToken })
        });
    }

}