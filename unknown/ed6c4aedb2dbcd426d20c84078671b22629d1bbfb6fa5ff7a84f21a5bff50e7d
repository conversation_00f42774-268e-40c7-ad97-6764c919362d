@import '../../global';

.mainContainer {
    width: 100px;
    height: 80px;

    .imageItem {
        width: 100%;
        height: 100%;
        object-fit: cover;
    }

    // 2 items
    &.twoItems {
        display: flex;
        .imageItem {
            width: 50%;
        }
    }

    // 3 items
    &.threeItems {
        display: grid;
        grid-template-columns: 50% 50%;
        grid-template-rows: 50% 50%;
        & > img:last-child {
            grid-column: 1 / 3;
        }
    }

    // 4 items
    &.fourItems {
        display: grid;
        grid-template-columns: 50% 50%;
        grid-template-rows: 50% 50%;
    }
}