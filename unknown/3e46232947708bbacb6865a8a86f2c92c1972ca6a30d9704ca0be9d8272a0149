    IF not organised by classes yet
In "EXAMPLE Utils.ts", the functions are grouped by classes.
Can you do the same in our Utils.ts.
Any new functions in our Utils.ts put in the general JC_Utils class.
Any new functions in the "EXAMPLE Utils.ts" put in our Utils.ts as well.

    ELSE
Look at "EXAMPLE Utils.ts".
Any new functions in our Utils.ts put in the general JC_Utils class.
Any new functions in the "EXAMPLE Utils.ts" put in our Utils.ts as well.