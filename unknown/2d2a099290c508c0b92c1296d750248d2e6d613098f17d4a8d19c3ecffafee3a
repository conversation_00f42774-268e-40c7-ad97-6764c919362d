import styles from "./JC_Title.module.scss";
import React from 'react';
import { JC_Utils } from "@/app/Utils";

export default function JC_Title(_: Readonly<{

    overrideClass?: string;
    title: string;
    isSecondary?: boolean;

}>) {
    return (

        <div className={`${styles.mainContainer} ${!JC_Utils.stringNullOrEmpty(_.overrideClass) ? _.overrideClass : ''} ${_.isSecondary ? styles.secondary : ""}`}>
            {_.title}
        </div>

    );
}
