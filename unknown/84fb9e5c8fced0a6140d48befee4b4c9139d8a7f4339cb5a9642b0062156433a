"use client"

import { JC_Utils } from "@/app/Utils";
import styles from "./JC_Radio.module.scss";
import React, { useState } from 'react';
import { JC_FieldOption } from '@/app/models/ComponentModels/JC_FieldOption';
import Image from "next/image";

// Individual Radio Button Component
export function JC_RadioButton(_: Readonly<{
    label?: string;
    checked?: boolean;
    onChange?: () => void;
}>) {
    return (
        <div className={`${styles.mainContainer} ${_.onChange != null ? styles.clickable : ""}`} onClick={_.onChange}>
            <div className={styles.radio}>
                {_.checked && <div className={styles.innerCheckedCircle} />}
            </div>
            {!JC_Utils.stringNullOrEmpty(_.label) && <label>{_.label}</label>}
        </div>
    );
}

// Radio Group Component
export default function JC_Radio(_: Readonly<{
    overrideClass?: string;
    label?: string;
    options: JC_FieldOption[];
    selectedOptionId?: string;
    onSelection: (newOptionId: string) => void;
    validate?: (value: string | number | undefined) => string;
}>) {
    // STATE
    const [selectedOption, setSelectedOption] = useState<JC_FieldOption | undefined>(
        !JC_Utils.stringNullOrEmpty(_.selectedOptionId)
            ? _.options.find(x => x.OptionId === _.selectedOptionId)
            : undefined
    );

    // HANDLES
    function handleSelection(optionId: string) {
        const newSelectedOption = _.options.find(x => x.OptionId === optionId);
        setSelectedOption(newSelectedOption);
        _.onSelection(optionId);
    }

    // BUILD
    function buildOptionContent(option: JC_FieldOption) {
        const isDisabled = option.Disabled === true;
        const handleClick = () => {
            if (!isDisabled) {
                handleSelection(option.OptionId);
            }
        };

        return (
            <div
                className={`${styles.mainContainer} ${!isDisabled ? styles.clickable : styles.disabled}`}
                key={option.OptionId}
                onClick={handleClick}
            >
                <div className={styles.radio}>
                    {selectedOption?.OptionId === option.OptionId && <div className={styles.innerCheckedCircle} />}
                </div>

                <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
                    {!JC_Utils.stringNullOrEmpty(option.IconName) &&
                    <Image
                        src={`/icons/${option.IconName}.webp`}
                        width={0}
                        height={0}
                        alt="Icon"
                        unoptimized
                    />}

                    {!JC_Utils.stringNullOrEmpty(option.Label) && <label>{option.Label}</label>}
                </div>
            </div>
        );
    }

    // MAIN
    return (
        <div className={`${styles.radioGroupContainer} ${!JC_Utils.stringNullOrEmpty(_.overrideClass) ? _.overrideClass : ''}`}>
            {/* Label */}
            {!JC_Utils.stringNullOrEmpty(_.label) &&
                <div className={styles.label}>
                    {_.label}
                    {_.validate != null && !JC_Utils.stringNullOrEmpty(_.validate(_.selectedOptionId)) &&
                        <span className={styles.errorSpan}>{_.validate(_.selectedOptionId)}</span>
                    }
                </div>
            }

            {/* Options */}
            <div className={styles.optionsContainer}>
                {_.options.map(option => buildOptionContent(option))}
            </div>
        </div>
    );
}
