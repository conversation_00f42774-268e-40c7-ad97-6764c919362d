import { NextRequest, NextResponse } from "next/server";
import { O_FurnishedModel } from "@/app/models/O_Furnished";
import { O_FurnishedBusiness } from "../business";

export async function PUT(request: NextRequest) {
    try {
        const dataList: O_FurnishedModel[] = await request.json();
        await O_FurnishedBusiness.CreateList(dataList);
        return NextResponse.json({ status: 200 });
    } catch (error) {
        console.log(error);
        return NextResponse.json({ error }, { status: 500 });
    }
}
