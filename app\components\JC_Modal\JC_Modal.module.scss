@import '../../global';

$topBottomPadding: 26px;

// Black background overlay
.blackOverlay {
    z-index: 998;
    width: 100%;
    height: 100%;
    position: fixed;
    top: 0; left: 0;
    background-color: $offBlack;
    opacity: 0.35;
}

// Modal
.modalContainer {
    z-index: 999;
    width: max-content;
    padding: $topBottomPadding 30px $topBottomPadding 30px;
    position: fixed;
    left: 50%; top: 50%; transform: translate(-50%, -50%);
    background-color: $offBlack;
    border: 2px solid $primaryColor;
    border-radius: $largeBorderRadius;
    box-sizing: border-box;
    @include containerShadow;
}
.modalContainer.forceTransparent {
    background-color: transparent;
    // Keep the border and shadow even when transparent
}

// Cancel button
.cancelButton {
    position: absolute;
    right: -44px;
    top: 2px;
    width: 18px;
    height: auto;
    padding: 5px;
    border-radius: 50%;
    color: $primaryColor;
    font-size: 27px;
    font-weight: bold;
    cursor: pointer;
}
.modalContainer.forceTransparent .cancelButton {
    right: -18px;
    top: 38px;
}

// Body content
.bodyContent {
    margin-top: 18px;
    color: $secondaryColor;
}

// - SCREEN SIZES - //

@media (max-width: $smallScreenSize) {
    .modalContainer {
        width: calc($tinyScreenSize - 150px)
    }
}

@media (max-width: $tinyScreenSize) {
    .modalContainer {
        width: calc($teenyTinyScreenSize - 150px);
    }
    .cancelButton {
        right: -35px;
    }
}

@media (max-width: $teenyTinyScreenSize) {
    .modalContainer {
        width: calc(100vw * 0.8);
    }
}