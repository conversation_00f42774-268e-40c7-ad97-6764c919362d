import { NextRequest, NextResponse } from "next/server";
import { unstable_noStore } from "next/cache";
import { UserPersistedDataModel } from "@/app/models/UserPersistedData";
import { UserPersistedDataBusiness } from "./business";

export const dynamic = 'force-dynamic';

// ------- //
// - GET - //
// ------- //

// Get by "Id", by "UserId", by "UserId and Code", or get all
export async function GET(request: NextRequest) {
    try {
        unstable_noStore();
        const params = new URL(request.url).searchParams;
        const id = params.get("id");
        const userId = params.get("userId");
        const code = params.get("code");
        const getAll = params.get("all") === "true";

        let result;
        if (getAll) {
            result = await UserPersistedDataBusiness.GetList();
        } else if (userId && code) {
            result = await UserPersistedDataBusiness.GetByUserIdAndCode(userId, code);
        } else if (userId) {
            result = await UserPersistedDataBusiness.GetByUserId(userId);
        } else if (id) {
            result = await UserPersistedDataBusiness.Get(id);
        } else {
            return NextResponse.json({ error: "Missing 'id', 'userId', or 'all' parameter" }, { status: 400 });
        }

        return NextResponse.json({ result }, { status: 200 });

    } catch (error) {
        console.log(error);
        return NextResponse.json({ error }, { status: 500 });
    }
}

// ---------- //
// - CREATE - //
// ---------- //

export async function PUT(request: NextRequest) {
    try {
        const params = new URL(request.url).searchParams;
        const isList = params.get("list") === "true";

        if (isList) {
            const dataList: UserPersistedDataModel[] = await request.json();
            await UserPersistedDataBusiness.CreateList(dataList);
        } else {
            const data: UserPersistedDataModel = await request.json();
            await UserPersistedDataBusiness.Create(data);
        }

        return NextResponse.json({ status: 200 });

    } catch (error) {
        console.log(error);
        return NextResponse.json({ error }, { status: 500 });
    }
}

// ---------- //
// - UPDATE - //
// ---------- //

export async function POST(request: NextRequest) {
    try {
        const params = new URL(request.url).searchParams;
        const valueOnly = params.get("valueOnly") === "true";

        if (valueOnly) {
            const { userId, code, value } = await request.json();
            await UserPersistedDataBusiness.UpdateValue(userId, code, value);
        } else {
            const data: UserPersistedDataModel = await request.json();
            await UserPersistedDataBusiness.Update(data);
        }

        return NextResponse.json({ status: 200 });

    } catch (error) {
        console.log(error);
        return NextResponse.json({ error }, { status: 500 });
    }
}

// ---------- //
// - DELETE - //
// ---------- //

export async function DELETE(request: NextRequest) {
    try {
        const params = new URL(request.url).searchParams;
        const id = params.get("id");
        const ids = params.get("ids");
        const userId = params.get("userId");
        const code = params.get("code");

        if (ids) {
            const idList: string[] = ids.split(',');
            await UserPersistedDataBusiness.DeleteList(idList);
        } else if (userId && code) {
            await UserPersistedDataBusiness.DeleteByUserIdAndCode(userId, code);
        } else if (id) {
            await UserPersistedDataBusiness.Delete(id);
        } else {
            return NextResponse.json({ error: "Missing 'id', 'ids', or 'userId and code' parameters" }, { status: 400 });
        }

        return NextResponse.json({ status: 200 });

    } catch (error) {
        console.log(error);
        return NextResponse.json({ error }, { status: 500 });
    }
}
