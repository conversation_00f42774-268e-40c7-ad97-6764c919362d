= = = = = = = = = = = = = = = =
Requirements
= = = = = = = = = = = = = = = =

User Logins: 1
Products: 0
About Us: 0
Our Clients: 0
Contact: 1
Payments: 0
Emails: 1
Show in Search Engines: 0


= = = = = = = = = = = = = = = =
What You Need To Do
= = = = = = = = = = = = = = = =

From the "Requirements" list above:
    - If "User Logins" is 0, completely remove all pages, backend logic, email templates and package.json packages related to Users/auth.
    - If "Products" is 0, completely remove all pages, backend logic and email templates related to Product Groups, Products, ProductVariations (including Variation Category) and Orders (including OrderStatus, OrderValuesAtSubmission etc.). Remove from Accounts page too, but do not delete the Accounts page.
    - If "About Us" is 0, completely remove the About Us page.
    - If "Our Clients" is 0, completely remove the Our Clients page.
    - If "Contact" is 0, completely remove all pages, backend logic and email templates related to Contact.
    - If "Payments" is 0, completely remove all pages, backend logic, email templates and package.json packages related to Payments/Stripe.
    - If "Emails" is 0, completely remove everything related to Emails, including package.json packages.
    - If "Show in Search Engines" is 0, make sure the site is not indexed by search engines.
Make sure you check every file in the project thoroughly.